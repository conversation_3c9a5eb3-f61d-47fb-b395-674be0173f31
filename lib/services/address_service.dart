import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../constants/app_constants.dart';
import '../models/address_model.dart';

class AddressService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all addresses for a user
  Future<List<AddressModel>> getUserAddresses(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(addressesCollection)
          .where('userId', isEqualTo: userId)
          .get();

      return snapshot.docs
          .map((doc) => AddressModel.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error getting user addresses: $e');
      return [];
    }
  }

  // Get a single address by ID
  Future<AddressModel?> getAddressById(String addressId) async {
    try {
      final doc = await _firestore
          .collection(addressesCollection)
          .doc(addressId)
          .get();

      if (doc.exists) {
        return AddressModel.fromMap(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting address by ID: $e');
      return null;
    }
  }

  // Create a new address
  Future<String?> createAddress(AddressModel address) async {
    try {
      // If this is set as default, unset any existing default addresses
      if (address.isDefault) {
        await _unsetDefaultAddresses(address.userId);
      }

      // Add coordinates if not provided
      AddressModel addressToSave = address;
      if (address.latitude == null || address.longitude == null) {
        final coordinates = await getCoordinatesFromAddress(address.formattedAddress);
        if (coordinates != null) {
          addressToSave = address.copyWith(
            latitude: coordinates['lat'],
            longitude: coordinates['lng'],
          );
        }
      }

      final docRef = await _firestore
          .collection(addressesCollection)
          .add(addressToSave.toMap());

      return docRef.id;
    } catch (e) {
      debugPrint('Error creating address: $e');
      return null;
    }
  }

  // Update an existing address
  Future<bool> updateAddress(AddressModel address) async {
    try {
      // If this is set as default, unset any existing default addresses
      if (address.isDefault) {
        await _unsetDefaultAddresses(address.userId);
      }

      // Add coordinates if not provided
      AddressModel addressToSave = address;
      if (address.latitude == null || address.longitude == null) {
        final coordinates = await getCoordinatesFromAddress(address.formattedAddress);
        if (coordinates != null) {
          addressToSave = address.copyWith(
            latitude: coordinates['lat'],
            longitude: coordinates['lng'],
          );
        }
      }

      await _firestore
          .collection(addressesCollection)
          .doc(address.id)
          .update(addressToSave.toMap());

      return true;
    } catch (e) {
      debugPrint('Error updating address: $e');
      return false;
    }
  }

  // Delete an address
  Future<bool> deleteAddress(String addressId) async {
    try {
      await _firestore
          .collection(addressesCollection)
          .doc(addressId)
          .delete();

      return true;
    } catch (e) {
      debugPrint('Error deleting address: $e');
      return false;
    }
  }

  // Set an address as default
  Future<bool> setDefaultAddress(String userId, String addressId) async {
    try {
      // Unset any existing default addresses
      await _unsetDefaultAddresses(userId);

      // Set the new default address
      await _firestore
          .collection(addressesCollection)
          .doc(addressId)
          .update({'isDefault': true});

      return true;
    } catch (e) {
      debugPrint('Error setting default address: $e');
      return false;
    }
  }

  // Unset all default addresses for a user
  Future<void> _unsetDefaultAddresses(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(addressesCollection)
          .where('userId', isEqualTo: userId)
          .where('isDefault', isEqualTo: true)
          .get();

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {'isDefault': false});
      }

      await batch.commit();
    } catch (e) {
      debugPrint('Error unsetting default addresses: $e');
    }
  }

  // Get the default address for a user
  Future<AddressModel?> getDefaultAddress(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(addressesCollection)
          .where('userId', isEqualTo: userId)
          .where('isDefault', isEqualTo: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return AddressModel.fromMap(snapshot.docs.first.data(), snapshot.docs.first.id);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting default address: $e');
      return null;
    }
  }

  // Parse an address string and save it as an AddressModel
  Future<AddressModel?> parseAndSaveAddress(String addressString, String userId) async {
    try {
      final parsedAddress = AddressModel.parseFromString(addressString, userId);
      if (parsedAddress == null) {
        return null;
      }

      // Get coordinates for the address
      final coordinates = await getCoordinatesFromAddress(parsedAddress.formattedAddress);
      AddressModel addressToSave = parsedAddress;
      if (coordinates != null) {
        addressToSave = parsedAddress.copyWith(
          latitude: coordinates['lat'],
          longitude: coordinates['lng'],
        );
      }

      final addressId = await createAddress(addressToSave);
      if (addressId != null) {
        // Create a new instance with the correct ID
        return AddressModel(
          id: addressId,
          userId: addressToSave.userId,
          name: addressToSave.name,
          addressLine1: addressToSave.addressLine1,
          addressLine2: addressToSave.addressLine2,
          city: addressToSave.city,
          state: addressToSave.state,
          postalCode: addressToSave.postalCode,
          country: addressToSave.country,
          phoneNumber: addressToSave.phoneNumber,
          isDefault: addressToSave.isDefault,
          latitude: addressToSave.latitude,
          longitude: addressToSave.longitude,
          createdAt: addressToSave.createdAt,
          updatedAt: addressToSave.updatedAt,
        );
      }
      return null;
    } catch (e) {
      debugPrint('Error parsing and saving address: $e');
      return null;
    }
  }

  // Get coordinates (latitude and longitude) from an address string using a geocoding service
  Future<Map<String, double>?> getCoordinatesFromAddress(String address) async {
    try {
      debugPrint('Getting coordinates for address: $address');

      // If the address is too short, return default coordinates for India
      if (address.length < 5) {
        debugPrint('Address too short, using default coordinates for India');
        return {'lat': 20.5937, 'lng': 78.9629}; // Default coordinates for India
      }

      // Try multiple geocoding services for better results

      // First try: OpenStreetMap Nominatim
      try {
        final encodedAddress = Uri.encodeComponent(address);
        final url = 'https://nominatim.openstreetmap.org/search?q=$encodedAddress&format=json&limit=1';

        final response = await http.get(
          Uri.parse(url),
          headers: {'User-Agent': 'Aster Computers Admin App'},
        );

        if (response.statusCode == 200) {
          final List<dynamic> data = json.decode(response.body);
          debugPrint('Nominatim response: $data');

          if (data.isNotEmpty) {
            final lat = double.tryParse(data[0]['lat']);
            final lng = double.tryParse(data[0]['lon']);

            if (lat != null && lng != null) {
              debugPrint('Found coordinates: $lat, $lng');
              return {'lat': lat, 'lng': lng};
            }
          }
        }
      } catch (e) {
        debugPrint('Error with Nominatim geocoding: $e');
      }

      // Second try: Fallback to a simple geocoding approach for Indian addresses
      // This is a very simplified approach that tries to extract city/state information
      // and use predefined coordinates for major Indian cities
      try {
        final result = _getApproximateCoordinatesForIndianAddress(address);
        if (result != null) {
          debugPrint('Using approximate coordinates: ${result['lat']}, ${result['lng']}');
          return result;
        }
      } catch (e) {
        debugPrint('Error with approximate geocoding: $e');
      }

      // Last resort: Return default coordinates for India
      debugPrint('All geocoding attempts failed, using default coordinates for India');
      return {'lat': 20.5937, 'lng': 78.9629}; // Default coordinates for India
    } catch (e) {
      debugPrint('Error getting coordinates from address: $e');
      // Return default coordinates for India as a fallback
      return {'lat': 20.5937, 'lng': 78.9629};
    }
  }

  // A simple method to get approximate coordinates for Indian addresses
  // This is a fallback when the geocoding service fails
  Map<String, double>? _getApproximateCoordinatesForIndianAddress(String address) {
    // Map of major Indian cities and their approximate coordinates
    final Map<String, Map<String, double>> cityCoordinates = {
      'mumbai': {'lat': 19.0760, 'lng': 72.8777},
      'delhi': {'lat': 28.6139, 'lng': 77.2090},
      'bangalore': {'lat': 12.9716, 'lng': 77.5946},
      'bengaluru': {'lat': 12.9716, 'lng': 77.5946},
      'hyderabad': {'lat': 17.3850, 'lng': 78.4867},
      'chennai': {'lat': 13.0827, 'lng': 80.2707},
      'kolkata': {'lat': 22.5726, 'lng': 88.3639},
      'pune': {'lat': 18.5204, 'lng': 73.8567},
      'ahmedabad': {'lat': 23.0225, 'lng': 72.5714},
      'jaipur': {'lat': 26.9124, 'lng': 75.7873},
      'lucknow': {'lat': 26.8467, 'lng': 80.9462},
      'kochi': {'lat': 9.9312, 'lng': 76.2673},
      'chandigarh': {'lat': 30.7333, 'lng': 76.7794},
      'coimbatore': {'lat': 11.0168, 'lng': 76.9558},
      'gurgaon': {'lat': 28.4595, 'lng': 77.0266},
      'noida': {'lat': 28.5355, 'lng': 77.3910},
      'goa': {'lat': 15.2993, 'lng': 74.1240},
      'nagpur': {'lat': 21.1458, 'lng': 79.0882},
      'indore': {'lat': 22.7196, 'lng': 75.8577},
      'bhopal': {'lat': 23.2599, 'lng': 77.4126},
      'visakhapatnam': {'lat': 17.6868, 'lng': 83.2185},
      'patna': {'lat': 25.5941, 'lng': 85.1376},
      'vadodara': {'lat': 22.3072, 'lng': 73.1812},
      'ludhiana': {'lat': 30.9010, 'lng': 75.8573},
      'agra': {'lat': 27.1767, 'lng': 78.0081},
      'nashik': {'lat': 19.9975, 'lng': 73.7898},
      'varanasi': {'lat': 25.3176, 'lng': 82.9739},
      'surat': {'lat': 21.1702, 'lng': 72.8311},
      'kanpur': {'lat': 26.4499, 'lng': 80.3319},
      'thiruvananthapuram': {'lat': 8.5241, 'lng': 76.9366},
      'trivandrum': {'lat': 8.5241, 'lng': 76.9366},
    };

    // Map of Indian states and their approximate coordinates
    final Map<String, Map<String, double>> stateCoordinates = {
      'maharashtra': {'lat': 19.7515, 'lng': 75.7139},
      'delhi': {'lat': 28.7041, 'lng': 77.1025},
      'karnataka': {'lat': 15.3173, 'lng': 75.7139},
      'telangana': {'lat': 18.1124, 'lng': 79.0193},
      'tamil nadu': {'lat': 11.1271, 'lng': 78.6569},
      'west bengal': {'lat': 22.9868, 'lng': 87.8550},
      'gujarat': {'lat': 22.2587, 'lng': 71.1924},
      'rajasthan': {'lat': 27.0238, 'lng': 74.2179},
      'uttar pradesh': {'lat': 26.8467, 'lng': 80.9462},
      'kerala': {'lat': 10.8505, 'lng': 76.2711},
      'punjab': {'lat': 31.1471, 'lng': 75.3412},
      'haryana': {'lat': 29.0588, 'lng': 76.0856},
      'bihar': {'lat': 25.0961, 'lng': 85.3131},
      'andhra pradesh': {'lat': 15.9129, 'lng': 79.7400},
      'madhya pradesh': {'lat': 22.9734, 'lng': 78.6569},
      'odisha': {'lat': 20.9517, 'lng': 85.0985},
      'assam': {'lat': 26.2006, 'lng': 92.9376},
      'jammu and kashmir': {'lat': 33.7782, 'lng': 76.5762},
      'goa': {'lat': 15.2993, 'lng': 74.1240},
      'uttarakhand': {'lat': 30.0668, 'lng': 79.0193},
      'jharkhand': {'lat': 23.6102, 'lng': 85.2799},
      'himachal pradesh': {'lat': 31.1048, 'lng': 77.1734},
      'chhattisgarh': {'lat': 21.2787, 'lng': 81.8661},
      'tripura': {'lat': 23.9408, 'lng': 91.9882},
      'manipur': {'lat': 24.6637, 'lng': 93.9063},
      'meghalaya': {'lat': 25.4670, 'lng': 91.3662},
      'nagaland': {'lat': 26.1584, 'lng': 94.5624},
      'sikkim': {'lat': 27.5330, 'lng': 88.5122},
      'mizoram': {'lat': 23.1645, 'lng': 92.9376},
      'arunachal pradesh': {'lat': 28.2180, 'lng': 94.7278},
    };

    // Convert address to lowercase for case-insensitive matching
    final lowerAddress = address.toLowerCase();

    // Check for city names in the address
    for (final city in cityCoordinates.keys) {
      if (lowerAddress.contains(city)) {
        return cityCoordinates[city];
      }
    }

    // Check for state names in the address
    for (final state in stateCoordinates.keys) {
      if (lowerAddress.contains(state)) {
        return stateCoordinates[state];
      }
    }

    // If no match found, return null
    return null;
  }
}
