import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import '../models/user.dart';

class CustomerService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Singleton pattern
  static final CustomerService _instance = CustomerService._internal();
  factory CustomerService() => _instance;
  CustomerService._internal();

  // Get all customers
  Future<List<AppUser>> getAllCustomers() async {
    try {
      final snapshot = await _firestore.collection(usersCollection).get();
      
      final customers = snapshot.docs.map((doc) {
        try {
          return AppUser.fromMap(doc.data(), doc.id);
        } catch (e) {
          debugPrint('Error parsing customer document ${doc.id}: $e');
          // Return a minimal valid user if parsing fails
          return AppUser(
            id: doc.id,
            email: doc.data()['email'] as String? ?? '<EMAIL>',
            favouriteProducts: [],
            createdAt: DateTime.now(),
          );
        }
      }).toList();
      
      return customers;
    } catch (e) {
      debugPrint('Error getting all customers: $e');
      return [];
    }
  }

  // Get customer by ID
  Future<AppUser?> getCustomerById(String customerId) async {
    try {
      if (customerId.isEmpty) {
        debugPrint('Customer ID is empty');
        return null;
      }

      final docSnapshot = await _firestore.collection(usersCollection).doc(customerId).get();
      
      if (!docSnapshot.exists) {
        debugPrint('Customer not found: $customerId');
        return null;
      }
      
      return AppUser.fromMap(docSnapshot.data()!, docSnapshot.id);
    } catch (e) {
      debugPrint('Error getting customer by ID: $e');
      return null;
    }
  }

  // Create or update customer
  Future<bool> createOrUpdateCustomer({
    required String customerId,
    required String email,
    String? displayName,
    String? phoneNumber,
  }) async {
    try {
      if (customerId.isEmpty) {
        debugPrint('Customer ID cannot be empty');
        return false;
      }

      if (email.isEmpty) {
        debugPrint('Email cannot be empty');
        return false;
      }

      // Check if customer exists
      final docSnapshot = await _firestore.collection(usersCollection).doc(customerId).get();
      
      final Map<String, dynamic> customerData = {
        'email': email,
        'isActive': true,
      };
      
      if (displayName != null && displayName.isNotEmpty) {
        customerData['displayName'] = displayName;
      }
      
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        customerData['phone'] = phoneNumber;
      }
      
      // Add timestamps only if the document doesn't exist
      if (!docSnapshot.exists) {
        customerData['createdAt'] = FieldValue.serverTimestamp();
        customerData['lastLogin'] = FieldValue.serverTimestamp();
        customerData['favourite_products'] = [];
      }
      
      await _firestore.collection(usersCollection).doc(customerId).set(
        customerData,
        SetOptions(merge: true),
      );
      
      debugPrint('Customer ${docSnapshot.exists ? 'updated' : 'created'}: $customerId');
      return true;
    } catch (e) {
      debugPrint('Error creating/updating customer: $e');
      return false;
    }
  }

  // Fix order with missing customer ID
  Future<bool> fixOrderWithMissingCustomerId(String orderId, String customerId) async {
    try {
      if (orderId.isEmpty) {
        debugPrint('Order ID cannot be empty');
        return false;
      }

      if (customerId.isEmpty) {
        debugPrint('Customer ID cannot be empty');
        return false;
      }

      // Check if customer exists
      final customerDoc = await _firestore.collection(usersCollection).doc(customerId).get();
      if (!customerDoc.exists) {
        debugPrint('Customer $customerId does not exist');
        return false;
      }

      // Find the order in the root collection
      final orderDoc = await _firestore.collection(ordersCollection).doc(orderId).get();
      
      if (!orderDoc.exists) {
        debugPrint('Order $orderId not found in root collection');
        return false;
      }

      // Get the order data
      final orderData = orderDoc.data();
      if (orderData == null) {
        debugPrint('Order data is null');
        return false;
      }

      // Update the order with the customer ID
      orderData['userId'] = customerId;

      // Create the order in the customer's collection
      final customerOrderRef = _firestore
          .collection(usersCollection)
          .doc(customerId)
          .collection(ordersCollection)
          .doc(orderId);
      
      await customerOrderRef.set(orderData);
      
      // Optionally delete the original order
      await _firestore.collection(ordersCollection).doc(orderId).delete();
      
      debugPrint('Fixed order $orderId by assigning it to customer $customerId');
      return true;
    } catch (e) {
      debugPrint('Error fixing order with missing customer ID: $e');
      return false;
    }
  }

  // Fix all orders with missing customer IDs
  Future<int> fixAllOrdersWithMissingCustomerIds(String customerId) async {
    try {
      if (customerId.isEmpty) {
        debugPrint('Customer ID cannot be empty');
        return 0;
      }

      // Check if customer exists
      final customerDoc = await _firestore.collection(usersCollection).doc(customerId).get();
      if (!customerDoc.exists) {
        debugPrint('Customer $customerId does not exist');
        return 0;
      }

      // Get all orders from the root collection
      final ordersSnapshot = await _firestore.collection(ordersCollection).get();
      
      int fixedCount = 0;
      
      for (final orderDoc in ordersSnapshot.docs) {
        final orderData = orderDoc.data();
        
        // Check if the order has an empty or missing userId
        if (orderData['userId'] == null || orderData['userId'] == '') {
          // Update the order with the customer ID
          orderData['userId'] = customerId;
          
          // Create the order in the customer's collection
          final customerOrderRef = _firestore
              .collection(usersCollection)
              .doc(customerId)
              .collection(ordersCollection)
              .doc(orderDoc.id);
          
          await customerOrderRef.set(orderData);
          
          // Delete the original order
          await _firestore.collection(ordersCollection).doc(orderDoc.id).delete();
          
          fixedCount++;
        }
      }
      
      debugPrint('Fixed $fixedCount orders with missing customer IDs');
      return fixedCount;
    } catch (e) {
      debugPrint('Error fixing orders with missing customer IDs: $e');
      return 0;
    }
  }
}
