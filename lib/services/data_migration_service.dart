import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../constants/shared_constants.dart';
import '../models/enhanced_order_model.dart';

/// Service to migrate and standardize data between Admin and User app formats
class DataMigrationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Singleton pattern
  static final DataMigrationService _instance = DataMigrationService._internal();
  factory DataMigrationService() => _instance;
  DataMigrationService._internal();

  /// Migrate all orders to standardized format
  Future<MigrationResult> migrateAllOrders() async {
    try {
      debugPrint('Starting order migration...');
      
      int totalProcessed = 0;
      int successfulMigrations = 0;
      int errors = 0;
      List<String> errorMessages = [];

      // Step 1: Migrate orders from root collection
      final rootResult = await _migrateRootCollectionOrders();
      totalProcessed += rootResult.totalProcessed;
      successfulMigrations += rootResult.successfulMigrations;
      errors += rootResult.errors;
      errorMessages.addAll(rootResult.errorMessages);

      // Step 2: Migrate orders from user subcollections
      final userResult = await _migrateUserSubcollectionOrders();
      totalProcessed += userResult.totalProcessed;
      successfulMigrations += userResult.successfulMigrations;
      errors += userResult.errors;
      errorMessages.addAll(userResult.errorMessages);

      // Step 3: Fix orphaned orders (orders without valid user IDs)
      final orphanResult = await _fixOrphanedOrders();
      totalProcessed += orphanResult.totalProcessed;
      successfulMigrations += orphanResult.successfulMigrations;
      errors += orphanResult.errors;
      errorMessages.addAll(orphanResult.errorMessages);

      debugPrint('Migration completed: $successfulMigrations/$totalProcessed successful');

      return MigrationResult(
        totalProcessed: totalProcessed,
        successfulMigrations: successfulMigrations,
        errors: errors,
        errorMessages: errorMessages,
      );
    } catch (e) {
      debugPrint('Error during migration: $e');
      return MigrationResult(
        totalProcessed: 0,
        successfulMigrations: 0,
        errors: 1,
        errorMessages: ['Migration failed: $e'],
      );
    }
  }

  /// Migrate orders from root collection to standardized format
  Future<MigrationResult> _migrateRootCollectionOrders() async {
    try {
      debugPrint('Migrating orders from root collection...');
      
      final snapshot = await _firestore.collection(FirebaseCollections.orders).get();
      int processed = 0;
      int successful = 0;
      int errors = 0;
      List<String> errorMessages = [];

      for (final doc in snapshot.docs) {
        processed++;
        try {
          final data = doc.data();
          
          // Check if order needs migration
          if (_needsMigration(data)) {
            final migratedData = _standardizeOrderData(data);
            
            // Update the document with standardized data
            await doc.reference.update(migratedData);
            successful++;
            
            debugPrint('Migrated order ${doc.id} in root collection');
          } else {
            successful++; // Already in correct format
          }
        } catch (e) {
          errors++;
          final errorMsg = 'Error migrating order ${doc.id}: $e';
          errorMessages.add(errorMsg);
          debugPrint(errorMsg);
        }
      }

      return MigrationResult(
        totalProcessed: processed,
        successfulMigrations: successful,
        errors: errors,
        errorMessages: errorMessages,
      );
    } catch (e) {
      debugPrint('Error migrating root collection: $e');
      return MigrationResult(
        totalProcessed: 0,
        successfulMigrations: 0,
        errors: 1,
        errorMessages: ['Root collection migration failed: $e'],
      );
    }
  }

  /// Migrate orders from user subcollections
  Future<MigrationResult> _migrateUserSubcollectionOrders() async {
    try {
      debugPrint('Migrating orders from user subcollections...');
      
      final usersSnapshot = await _firestore.collection(FirebaseCollections.users).get();
      int processed = 0;
      int successful = 0;
      int errors = 0;
      List<String> errorMessages = [];

      for (final userDoc in usersSnapshot.docs) {
        try {
          final ordersSnapshot = await _firestore
              .collection(FirebaseCollections.users)
              .doc(userDoc.id)
              .collection(FirebaseCollections.orders)
              .get();

          for (final orderDoc in ordersSnapshot.docs) {
            processed++;
            try {
              final data = orderDoc.data();
              
              // Ensure userId is set correctly
              data[OrderFields.userId] = userDoc.id;
              data[OrderFields.userIdAlt] = userDoc.id;
              
              if (_needsMigration(data)) {
                final migratedData = _standardizeOrderData(data);
                
                // Update the document
                await orderDoc.reference.update(migratedData);
                successful++;
                
                debugPrint('Migrated order ${orderDoc.id} for user ${userDoc.id}');
              } else {
                successful++; // Already in correct format
              }
            } catch (e) {
              errors++;
              final errorMsg = 'Error migrating order ${orderDoc.id} for user ${userDoc.id}: $e';
              errorMessages.add(errorMsg);
              debugPrint(errorMsg);
            }
          }
        } catch (e) {
          errors++;
          final errorMsg = 'Error processing user ${userDoc.id}: $e';
          errorMessages.add(errorMsg);
          debugPrint(errorMsg);
        }
      }

      return MigrationResult(
        totalProcessed: processed,
        successfulMigrations: successful,
        errors: errors,
        errorMessages: errorMessages,
      );
    } catch (e) {
      debugPrint('Error migrating user subcollections: $e');
      return MigrationResult(
        totalProcessed: 0,
        successfulMigrations: 0,
        errors: 1,
        errorMessages: ['User subcollection migration failed: $e'],
      );
    }
  }

  /// Fix orphaned orders (orders without valid user IDs)
  Future<MigrationResult> _fixOrphanedOrders() async {
    try {
      debugPrint('Fixing orphaned orders...');
      
      final snapshot = await _firestore
          .collection(FirebaseCollections.orders)
          .where(OrderFields.userId, isEqualTo: '')
          .get();

      int processed = snapshot.docs.length;
      int successful = 0;
      int errors = 0;
      List<String> errorMessages = [];

      if (processed == 0) {
        // Also check for orders with null userId
        final nullSnapshot = await _firestore
            .collection(FirebaseCollections.orders)
            .where(OrderFields.userId, isNull: true)
            .get();
        
        processed = nullSnapshot.docs.length;
        
        for (final doc in nullSnapshot.docs) {
          try {
            // Mark as orphaned for manual review
            await doc.reference.update({
              OrderFields.userId: 'ORPHANED_ORDER',
              OrderFields.userIdAlt: 'ORPHANED_ORDER',
              'migration_status': 'orphaned',
              'migration_timestamp': Timestamp.now(),
            });
            successful++;
          } catch (e) {
            errors++;
            errorMessages.add('Error marking order ${doc.id} as orphaned: $e');
          }
        }
      } else {
        for (final doc in snapshot.docs) {
          try {
            // Mark as orphaned for manual review
            await doc.reference.update({
              OrderFields.userId: 'ORPHANED_ORDER',
              OrderFields.userIdAlt: 'ORPHANED_ORDER',
              'migration_status': 'orphaned',
              'migration_timestamp': Timestamp.now(),
            });
            successful++;
          } catch (e) {
            errors++;
            errorMessages.add('Error marking order ${doc.id} as orphaned: $e');
          }
        }
      }

      return MigrationResult(
        totalProcessed: processed,
        successfulMigrations: successful,
        errors: errors,
        errorMessages: errorMessages,
      );
    } catch (e) {
      debugPrint('Error fixing orphaned orders: $e');
      return MigrationResult(
        totalProcessed: 0,
        successfulMigrations: 0,
        errors: 1,
        errorMessages: ['Orphaned order fix failed: $e'],
      );
    }
  }

  /// Check if order data needs migration
  bool _needsMigration(Map<String, dynamic> data) {
    // Check if both field formats exist
    bool hasStandardFields = data.containsKey(OrderFields.userId) && 
                            data.containsKey(OrderFields.orderDate);
    bool hasAltFields = data.containsKey(OrderFields.userIdAlt) && 
                       data.containsKey(OrderFields.orderDateAlt);
    
    // Needs migration if missing either format
    return !(hasStandardFields && hasAltFields);
  }

  /// Standardize order data to include both field formats
  Map<String, dynamic> _standardizeOrderData(Map<String, dynamic> data) {
    final standardized = Map<String, dynamic>.from(data);

    // Ensure both userId formats exist
    if (!standardized.containsKey(OrderFields.userId) && standardized.containsKey(OrderFields.userIdAlt)) {
      standardized[OrderFields.userId] = standardized[OrderFields.userIdAlt];
    } else if (!standardized.containsKey(OrderFields.userIdAlt) && standardized.containsKey(OrderFields.userId)) {
      standardized[OrderFields.userIdAlt] = standardized[OrderFields.userId];
    }

    // Ensure both orderDate formats exist
    if (!standardized.containsKey(OrderFields.orderDate) && standardized.containsKey(OrderFields.orderDateAlt)) {
      standardized[OrderFields.orderDate] = standardized[OrderFields.orderDateAlt];
    } else if (!standardized.containsKey(OrderFields.orderDateAlt) && standardized.containsKey(OrderFields.orderDate)) {
      standardized[OrderFields.orderDateAlt] = standardized[OrderFields.orderDate];
    }

    // Add other field mappings as needed
    _mapField(standardized, OrderFields.productId, OrderFields.productIdAlt);
    _mapField(standardized, OrderFields.totalAmount, OrderFields.totalAmountAlt);
    _mapField(standardized, OrderFields.paymentMethod, OrderFields.paymentMethodAlt);
    _mapField(standardized, OrderFields.isPaid, OrderFields.isPaidAlt);

    // Add migration metadata
    standardized['migration_timestamp'] = Timestamp.now();
    standardized['migration_version'] = '1.0';

    return standardized;
  }

  /// Helper to map field values between formats
  void _mapField(Map<String, dynamic> data, String primaryField, String altField) {
    if (!data.containsKey(primaryField) && data.containsKey(altField)) {
      data[primaryField] = data[altField];
    } else if (!data.containsKey(altField) && data.containsKey(primaryField)) {
      data[altField] = data[primaryField];
    }
  }
}

/// Result of a migration operation
class MigrationResult {
  final int totalProcessed;
  final int successfulMigrations;
  final int errors;
  final List<String> errorMessages;

  MigrationResult({
    required this.totalProcessed,
    required this.successfulMigrations,
    required this.errors,
    required this.errorMessages,
  });

  bool get isSuccessful => errors == 0;
  double get successRate => totalProcessed > 0 ? successfulMigrations / totalProcessed : 0.0;
}
