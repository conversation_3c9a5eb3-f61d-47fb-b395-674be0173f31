import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';

import '../constants/app_constants.dart';
import 'order_service.dart';

class PaymentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final OrderService _orderService = OrderService();

  // Singleton pattern
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  // Razorpay configuration (should be moved to environment variables)
  static const String _razorpayKeyId = 'YOUR_RAZORPAY_KEY_ID';
  static const String _razorpayKeySecret = 'YOUR_RAZORPAY_KEY_SECRET';

  /// Verify Razorpay payment signature
  bool verifyPaymentSignature({
    required String orderId,
    required String paymentId,
    required String signature,
  }) {
    try {
      // Create the signature string
      final signatureString = '$orderId|$paymentId';
      
      // Generate HMAC SHA256 hash
      final key = utf8.encode(_razorpayKeySecret);
      final bytes = utf8.encode(signatureString);
      final hmacSha256 = Hmac(sha256, key);
      final digest = hmacSha256.convert(bytes);
      final generatedSignature = digest.toString();

      // Compare signatures
      return generatedSignature == signature;
    } catch (e) {
      debugPrint('Error verifying payment signature: $e');
      return false;
    }
  }

  /// Get payment details from Razorpay API
  Future<Map<String, dynamic>?> getPaymentDetails(String paymentId) async {
    try {
      if (_razorpayKeyId == 'YOUR_RAZORPAY_KEY_ID' || _razorpayKeySecret == 'YOUR_RAZORPAY_KEY_SECRET') {
        debugPrint('Razorpay credentials not configured');
        return null;
      }

      final credentials = base64Encode(utf8.encode('$_razorpayKeyId:$_razorpayKeySecret'));
      
      final response = await http.get(
        Uri.parse('https://api.razorpay.com/v1/payments/$paymentId'),
        headers: {
          'Authorization': 'Basic $credentials',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        debugPrint('Failed to get payment details: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error getting payment details: $e');
      return null;
    }
  }

  /// Get all payments for admin dashboard
  Future<List<Map<String, dynamic>>> getAllPayments() async {
    try {
      final List<Map<String, dynamic>> payments = [];

      // Get payments from orders collection (User app format)
      final ordersSnapshot = await _firestore.collection(ordersCollection).get();
      
      for (final doc in ordersSnapshot.docs) {
        final data = doc.data();
        
        // Check if order has payment information
        if (data.containsKey('paymentId') || data.containsKey('payment_id')) {
          final paymentInfo = {
            'orderId': doc.id,
            'paymentId': data['paymentId'] ?? data['payment_id'],
            'razorpayOrderId': data['razorpayOrderId'] ?? data['razorpay_order_id'] ?? data['order_id'],
            'paymentSignature': data['paymentSignature'] ?? data['payment_signature'] ?? data['signature'],
            'isVerified': data['isPaymentVerified'] ?? data['is_payment_verified'] ?? data['verified'] ?? false,
            'amount': data['total'] ?? data['amount'] ?? 0,
            'paymentMethod': data['payment_method'] ?? data['paymentMethod'] ?? 'razorpay',
            'status': data['order_status'] ?? data['status'] ?? 'unknown',
            'orderDate': data['order_date'] ?? data['orderDate'],
            'userId': data['user_id'] ?? data['userId'],
            'customerName': data['customer_name'] ?? data['customerName'],
            'customerEmail': data['customer_email'] ?? data['customerEmail'],
          };
          payments.add(paymentInfo);
        }
      }

      // Sort by order date (newest first)
      payments.sort((a, b) {
        final aDate = a['orderDate'];
        final bDate = b['orderDate'];
        
        if (aDate is Timestamp && bDate is Timestamp) {
          return bDate.compareTo(aDate);
        }
        return 0;
      });

      return payments;
    } catch (e) {
      debugPrint('Error getting all payments: $e');
      return [];
    }
  }

  /// Get payment statistics for dashboard
  Future<Map<String, dynamic>> getPaymentStatistics() async {
    try {
      final payments = await getAllPayments();
      
      int totalPayments = payments.length;
      int verifiedPayments = 0;
      int unverifiedPayments = 0;
      double totalAmount = 0.0;
      double verifiedAmount = 0.0;
      
      Map<String, int> statusCounts = {};
      Map<String, double> statusAmounts = {};

      for (final payment in payments) {
        final amount = (payment['amount'] ?? 0).toDouble();
        final status = payment['status']?.toString() ?? 'unknown';
        final isVerified = payment['isVerified'] ?? false;

        totalAmount += amount;
        
        if (isVerified) {
          verifiedPayments++;
          verifiedAmount += amount;
        } else {
          unverifiedPayments++;
        }

        statusCounts[status] = (statusCounts[status] ?? 0) + 1;
        statusAmounts[status] = (statusAmounts[status] ?? 0) + amount;
      }

      return {
        'total_payments': totalPayments,
        'verified_payments': verifiedPayments,
        'unverified_payments': unverifiedPayments,
        'total_amount': totalAmount,
        'verified_amount': verifiedAmount,
        'unverified_amount': totalAmount - verifiedAmount,
        'status_counts': statusCounts,
        'status_amounts': statusAmounts,
        'verification_rate': totalPayments > 0 ? (verifiedPayments / totalPayments * 100) : 0,
        'last_updated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error getting payment statistics: $e');
      return {
        'error': e.toString(),
        'total_payments': 0,
        'verified_payments': 0,
        'unverified_payments': 0,
        'total_amount': 0.0,
        'verified_amount': 0.0,
        'unverified_amount': 0.0,
        'verification_rate': 0.0,
      };
    }
  }

  /// Verify payment and update order
  Future<bool> verifyAndUpdatePayment({
    required String orderId,
    required String paymentId,
    required String signature,
  }) async {
    try {
      // Verify the payment signature
      final isSignatureValid = verifyPaymentSignature(
        orderId: orderId,
        paymentId: paymentId,
        signature: signature,
      );

      if (!isSignatureValid) {
        debugPrint('Invalid payment signature for order: $orderId');
        return false;
      }

      // Get payment details from Razorpay
      final paymentDetails = await getPaymentDetails(paymentId);
      
      if (paymentDetails == null) {
        debugPrint('Could not fetch payment details from Razorpay');
        // Still update as verified if signature is valid
      }

      // Update payment verification in order
      await _orderService.updatePaymentVerification(
        orderId,
        true,
        paymentId: paymentId,
        signature: signature,
      );

      debugPrint('Payment verified and updated for order: $orderId');
      return true;
    } catch (e) {
      debugPrint('Error verifying and updating payment: $e');
      return false;
    }
  }

  /// Bulk verify all unverified payments
  Future<Map<String, dynamic>> bulkVerifyPayments() async {
    try {
      final payments = await getAllPayments();
      final unverifiedPayments = payments.where((p) => !(p['isVerified'] ?? false)).toList();
      
      int successCount = 0;
      int failureCount = 0;
      List<String> errors = [];

      for (final payment in unverifiedPayments) {
        try {
          final orderId = payment['orderId'];
          final paymentId = payment['paymentId'];
          final signature = payment['paymentSignature'];

          if (orderId != null && paymentId != null && signature != null) {
            final success = await verifyAndUpdatePayment(
              orderId: orderId,
              paymentId: paymentId,
              signature: signature,
            );

            if (success) {
              successCount++;
            } else {
              failureCount++;
              errors.add('Failed to verify payment for order: $orderId');
            }
          } else {
            failureCount++;
            errors.add('Missing payment data for order: $orderId');
          }
        } catch (e) {
          failureCount++;
          errors.add('Error processing payment: $e');
        }
      }

      return {
        'total_processed': unverifiedPayments.length,
        'success_count': successCount,
        'failure_count': failureCount,
        'errors': errors,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Error in bulk verify payments: $e');
      return {
        'error': e.toString(),
        'total_processed': 0,
        'success_count': 0,
        'failure_count': 0,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
