import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import '../models/order_model.dart';
import 'order_service.dart';
import 'payment_service.dart';
import 'data_sync_service.dart';

class IntegrationTestService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final OrderService _orderService = OrderService();
  final PaymentService _paymentService = PaymentService();
  final DataSyncService _dataSyncService = DataSyncService();

  // Singleton pattern
  static final IntegrationTestService _instance = IntegrationTestService._internal();
  factory IntegrationTestService() => _instance;
  IntegrationTestService._internal();

  /// Run comprehensive integration tests
  Future<Map<String, dynamic>> runIntegrationTests() async {
    try {
      debugPrint('Starting comprehensive integration tests...');
      
      final results = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'overall_status': 'running',
        'tests': <String, dynamic>{},
        'summary': <String, dynamic>{},
        'recommendations': <String>[],
      };

      // Test 1: Data Structure Compatibility
      debugPrint('Running data structure compatibility test...');
      results['tests']['data_structure'] = await _testDataStructureCompatibility();

      // Test 2: Order Parsing and Display
      debugPrint('Running order parsing test...');
      results['tests']['order_parsing'] = await _testOrderParsing();

      // Test 3: Payment Integration
      debugPrint('Running payment integration test...');
      results['tests']['payment_integration'] = await _testPaymentIntegration();

      // Test 4: Status Update Synchronization
      debugPrint('Running status update test...');
      results['tests']['status_updates'] = await _testStatusUpdateSync();

      // Test 5: User Data Consistency
      debugPrint('Running user data consistency test...');
      results['tests']['user_data'] = await _testUserDataConsistency();

      // Test 6: Collection Structure Verification
      debugPrint('Running collection structure test...');
      results['tests']['collection_structure'] = await _testCollectionStructure();

      // Generate summary
      results['summary'] = _generateTestSummary(results['tests'] as Map<String, dynamic>);
      results['overall_status'] = results['summary']['overall_status'];
      results['recommendations'] = _generateRecommendations(results['tests'] as Map<String, dynamic>);

      debugPrint('Integration tests completed');
      return results;
    } catch (e) {
      debugPrint('Error running integration tests: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
        'overall_status': 'error',
      };
    }
  }

  /// Test data structure compatibility between apps
  Future<Map<String, dynamic>> _testDataStructureCompatibility() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'total_orders': 0,
        'compatible_orders': 0,
        'incompatible_orders': 0,
        'issues': <String>[],
      };

      // Get sample orders from user app collection
      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(50)
          .get();

      result['total_orders'] = ordersSnapshot.docs.length;

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();
          
          // Try to parse with admin app OrderModel
          OrderModel.fromMap(data, doc.id);
          result['compatible_orders'] = (result['compatible_orders'] as int) + 1;
          
        } catch (e) {
          result['incompatible_orders'] = (result['incompatible_orders'] as int) + 1;
          (result['issues'] as List<String>).add('Order ${doc.id}: $e');
        }
      }

      if (result['incompatible_orders'] > 0) {
        result['status'] = 'failed';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test order parsing functionality
  Future<Map<String, dynamic>> _testOrderParsing() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'single_item_orders': 0,
        'multi_item_orders': 0,
        'parsing_errors': 0,
        'field_coverage': <String, int>{},
        'issues': <String>[],
      };

      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(30)
          .get();

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();
          final order = OrderModel.fromMap(data, doc.id);
          
          // Check if it's single or multi-item
          if (order.items.length == 1) {
            result['single_item_orders'] = (result['single_item_orders'] as int) + 1;
          } else if (order.items.length > 1) {
            result['multi_item_orders'] = (result['multi_item_orders'] as int) + 1;
          }

          // Check field coverage
          final fields = [
            'customerName', 'customerEmail', 'customerPhone',
            'paymentId', 'razorpayOrderId', 'isPaymentVerified',
            'shippingAddress', 'trackingNumber'
          ];

          for (final field in fields) {
            if (_hasField(order, field)) {
              result['field_coverage'][field] = (result['field_coverage'][field] ?? 0) + 1;
            }
          }

        } catch (e) {
          result['parsing_errors'] = (result['parsing_errors'] as int) + 1;
          (result['issues'] as List<String>).add('Parsing error for ${doc.id}: $e');
        }
      }

      if (result['parsing_errors'] > 0) {
        result['status'] = 'failed';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test payment integration functionality
  Future<Map<String, dynamic>> _testPaymentIntegration() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'total_payments': 0,
        'razorpay_payments': 0,
        'verified_payments': 0,
        'unverified_payments': 0,
        'payment_fields_present': 0,
        'issues': <String>[],
      };

      final payments = await _paymentService.getAllPayments();
      result['total_payments'] = payments.length;

      for (final payment in payments) {
        if (payment['paymentMethod']?.toString().toLowerCase() == 'razorpay') {
          result['razorpay_payments'] = (result['razorpay_payments'] as int) + 1;
          
          if (payment['isVerified'] == true) {
            result['verified_payments'] = (result['verified_payments'] as int) + 1;
          } else {
            result['unverified_payments'] = (result['unverified_payments'] as int) + 1;
          }

          // Check if payment fields are present
          if (payment['paymentId'] != null && payment['razorpayOrderId'] != null) {
            result['payment_fields_present'] = (result['payment_fields_present'] as int) + 1;
          }
        }
      }

      // Check if payment service is working
      try {
        final stats = await _paymentService.getPaymentStatistics();
        if (stats.containsKey('error')) {
          (result['issues'] as List<String>).add('Payment statistics error: ${stats['error']}');
          result['status'] = 'warning';
        }
      } catch (e) {
        (result['issues'] as List<String>).add('Payment service error: $e');
        result['status'] = 'failed';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test status update synchronization
  Future<Map<String, dynamic>> _testStatusUpdateSync() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'test_performed': false,
        'sync_successful': false,
        'issues': <String>[],
      };

      // Find a test order to update (don't actually update, just verify the mechanism)
      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(1)
          .get();

      if (ordersSnapshot.docs.isNotEmpty) {
        final testOrder = ordersSnapshot.docs.first;
        final data = testOrder.data();
        
        // Verify that the order has the necessary fields for status updates
        final hasUserId = data.containsKey('user_id') || data.containsKey('userId');
        final hasStatus = data.containsKey('order_status') || data.containsKey('status');
        
        if (hasUserId && hasStatus) {
          result['test_performed'] = true;
          result['sync_successful'] = true;
        } else {
          (result['issues'] as List<String>).add('Test order missing required fields for status updates');
          result['status'] = 'warning';
        }
      } else {
        (result['issues'] as List<String>).add('No orders available for status update testing');
        result['status'] = 'warning';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test user data consistency
  Future<Map<String, dynamic>> _testUserDataConsistency() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'orders_with_user_data': 0,
        'orders_missing_user_data': 0,
        'user_id_consistency': 0,
        'customer_data_coverage': <String, int>{},
        'issues': <String>[],
      };

      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(30)
          .get();

      for (final doc in ordersSnapshot.docs) {
        final data = doc.data();
        
        // Check user ID consistency
        final hasUserId = data.containsKey('user_id') || data.containsKey('userId');
        if (hasUserId) {
          result['user_id_consistency'] = (result['user_id_consistency'] as int) + 1;
        }

        // Check customer data
        final customerFields = ['customer_name', 'customerName', 'customer_email', 'customerEmail'];
        bool hasCustomerData = false;
        
        for (final field in customerFields) {
          if (data.containsKey(field) && data[field] != null && data[field].toString().isNotEmpty) {
            hasCustomerData = true;
            result['customer_data_coverage'][field] = (result['customer_data_coverage'][field] ?? 0) + 1;
          }
        }

        if (hasCustomerData) {
          result['orders_with_user_data'] = (result['orders_with_user_data'] as int) + 1;
        } else {
          result['orders_missing_user_data'] = (result['orders_missing_user_data'] as int) + 1;
        }
      }

      if (result['orders_missing_user_data'] > result['orders_with_user_data']) {
        result['status'] = 'warning';
        (result['issues'] as List<String>).add('Many orders missing customer data');
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test collection structure
  Future<Map<String, dynamic>> _testCollectionStructure() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'collections_found': <String>[],
        'collection_counts': <String, int>{},
        'issues': <String>[],
      };

      // Check main collections
      final collections = ['orders', 'ordered_products', 'users', 'products', 'admin_orders'];
      
      for (final collection in collections) {
        try {
          final snapshot = await _firestore.collection(collection).limit(1).get();
          result['collections_found'].add(collection);
          
          final countSnapshot = await _firestore.collection(collection).count().get();
          result['collection_counts'][collection] = countSnapshot.count ?? 0;
          
        } catch (e) {
          (result['issues'] as List<String>).add('Collection $collection not accessible: $e');
        }
      }

      // Verify that we have the essential collections
      final essentialCollections = ['orders', 'users', 'products'];
      for (final essential in essentialCollections) {
        if (!(result['collections_found'] as List).contains(essential)) {
          result['status'] = 'failed';
          (result['issues'] as List<String>).add('Essential collection $essential not found');
        }
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Generate test summary
  Map<String, dynamic> _generateTestSummary(Map<String, dynamic> tests) {
    int passed = 0;
    int failed = 0;
    int warnings = 0;
    int errors = 0;

    for (final test in tests.values) {
      if (test is Map<String, dynamic>) {
        switch (test['status']) {
          case 'passed':
            passed++;
            break;
          case 'failed':
            failed++;
            break;
          case 'warning':
            warnings++;
            break;
          case 'error':
            errors++;
            break;
        }
      }
    }

    String overallStatus = 'passed';
    if (errors > 0) {
      overallStatus = 'error';
    } else if (failed > 0) {
      overallStatus = 'failed';
    } else if (warnings > 0) {
      overallStatus = 'warning';
    }

    return {
      'total_tests': tests.length,
      'passed': passed,
      'failed': failed,
      'warnings': warnings,
      'errors': errors,
      'overall_status': overallStatus,
      'success_rate': tests.isNotEmpty ? (passed / tests.length * 100).toStringAsFixed(1) : '0',
    };
  }

  /// Generate recommendations based on test results
  List<String> _generateRecommendations(Map<String, dynamic> tests) {
    final recommendations = <String>[];

    // Check data structure test
    if (tests['data_structure']?['status'] == 'failed') {
      recommendations.add('Run data synchronization to fix incompatible order structures');
    }

    // Check payment integration
    if (tests['payment_integration']?['unverified_payments'] > 0) {
      recommendations.add('Run bulk payment verification to verify unverified payments');
    }

    // Check user data consistency
    if (tests['user_data']?['orders_missing_user_data'] > 0) {
      recommendations.add('Run data synchronization to extract missing customer data');
    }

    // Check collection structure
    if (tests['collection_structure']?['status'] == 'failed') {
      recommendations.add('Verify Firebase security rules and collection permissions');
    }

    // General recommendations
    if (recommendations.isEmpty) {
      recommendations.add('All tests passed! Cross-app integration is working well.');
    } else {
      recommendations.add('Use the Data Sync page to fix most of these issues automatically');
    }

    return recommendations;
  }

  /// Helper method to check if order has a specific field
  bool _hasField(OrderModel order, String field) {
    switch (field) {
      case 'customerName':
        return order.customerName != null && order.customerName!.isNotEmpty;
      case 'customerEmail':
        return order.customerEmail != null && order.customerEmail!.isNotEmpty;
      case 'customerPhone':
        return order.customerPhone != null && order.customerPhone!.isNotEmpty;
      case 'paymentId':
        return order.paymentId != null && order.paymentId!.isNotEmpty;
      case 'razorpayOrderId':
        return order.razorpayOrderId != null && order.razorpayOrderId!.isNotEmpty;
      case 'isPaymentVerified':
        return true; // This field always exists (defaults to false)
      case 'shippingAddress':
        return order.shippingAddress != null && order.shippingAddress!.isNotEmpty;
      case 'trackingNumber':
        return order.trackingNumber != null && order.trackingNumber!.isNotEmpty;
      default:
        return false;
    }
  }
}
