import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import '../models/order_model.dart';
import 'data_sync_service.dart';
import 'order_service.dart';
import 'payment_service.dart';

class IntegrationTestService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final OrderService _orderService = OrderService();
  final PaymentService _paymentService = PaymentService();
  final DataSyncService _dataSyncService = DataSyncService();

  // Singleton pattern
  static final IntegrationTestService _instance = IntegrationTestService._internal();
  factory IntegrationTestService() => _instance;
  IntegrationTestService._internal();

  /// Run comprehensive integration tests
  Future<Map<String, dynamic>> runIntegrationTests() async {
    try {
      debugPrint('🚀 Starting comprehensive cross-app integration tests...');

      final results = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'overall_status': 'running',
        'tests': <String, dynamic>{},
        'summary': <String, dynamic>{},
        'recommendations': <String>[],
      };

      // Test 1: ID Consistency (HIGHEST PRIORITY)
      debugPrint('🔍 Running ID consistency test...');
      results['tests']['id_consistency'] = await _testIdConsistency();

      // Test 2: Data Structure Compatibility
      debugPrint('🔍 Running data structure compatibility test...');
      results['tests']['data_structure'] = await _testDataStructureCompatibility();

      // Test 3: Order Parsing and Display
      debugPrint('🔍 Running order parsing test...');
      results['tests']['order_parsing'] = await _testOrderParsing();

      // Test 4: Address Integration
      debugPrint('🔍 Running address integration test...');
      results['tests']['address_integration'] = await _testAddressIntegration();

      // Test 5: Status Update Synchronization
      debugPrint('🔍 Running status update synchronization test...');
      results['tests']['status_updates'] = await _testStatusUpdateSync();

      // Test 6: User Data Consistency
      debugPrint('🔍 Running user data consistency test...');
      results['tests']['user_data'] = await _testUserDataConsistency();

      // Test 7: Payment Integration
      debugPrint('🔍 Running payment integration test...');
      results['tests']['payment_integration'] = await _testPaymentIntegration();

      // Test 8: Collection Structure Verification
      debugPrint('🔍 Running collection structure test...');
      results['tests']['collection_structure'] = await _testCollectionStructure();

      // Test 9: Cross-App Order Lifecycle
      debugPrint('🔍 Running cross-app order lifecycle test...');
      results['tests']['order_lifecycle'] = await _testOrderLifecycle();

      // Test 10: Order Data Parsing
      debugPrint('🔍 Running order data parsing test...');
      results['tests']['order_parsing_fix'] = await _testOrderDataParsing();

      // Test 11: Real-Time Data Verification
      debugPrint('🔍 Running real-time data verification...');
      results['tests']['real_time_verification'] = await _testRealTimeDataVerification();

      // Test 12: Sample Data Parsing Test
      debugPrint('🔍 Running sample data parsing test...');
      results['tests']['sample_data_parsing'] = _testSampleDataParsing();

      // Generate summary
      results['summary'] = _generateTestSummary(results['tests'] as Map<String, dynamic>);
      results['overall_status'] = results['summary']['overall_status'];
      results['recommendations'] = _generateRecommendations(results['tests'] as Map<String, dynamic>);

      debugPrint('✅ Integration tests completed');
      return results;
    } catch (e) {
      debugPrint('💥 Error running integration tests: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
        'overall_status': 'error',
      };
    }
  }

  /// Test ID consistency between user and admin apps (HIGHEST PRIORITY)
  Future<Map<String, dynamic>> _testIdConsistency() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'total_orders': 0,
        'orders_with_user_id': 0,
        'orders_with_consistent_ids': 0,
        'id_field_coverage': <String, int>{},
        'issues': <String>[],
      };

      // Get sample orders from primary collection
      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(30)
          .get();

      result['total_orders'] = ordersSnapshot.docs.length;

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();

          // Check for user ID fields
          final hasUserId = data.containsKey('user_id');
          final hasUserIdAlt = data.containsKey('userId');

          if (hasUserId || hasUserIdAlt) {
            result['orders_with_user_id'] = (result['orders_with_user_id'] as int) + 1;

            // Check ID consistency
            if (hasUserId && hasUserIdAlt) {
              final userId1 = data['user_id'];
              final userId2 = data['userId'];

              if (userId1 == userId2) {
                result['orders_with_consistent_ids'] = (result['orders_with_consistent_ids'] as int) + 1;
              } else {
                (result['issues'] as List<String>).add('Order ${doc.id}: user_id ($userId1) != userId ($userId2)');
              }
            } else if (hasUserId) {
              result['id_field_coverage']['user_id'] = (result['id_field_coverage']['user_id'] ?? 0) + 1;
            } else if (hasUserIdAlt) {
              result['id_field_coverage']['userId'] = (result['id_field_coverage']['userId'] ?? 0) + 1;
            }
          } else {
            (result['issues'] as List<String>).add('Order ${doc.id}: Missing user ID fields');
          }

          // Check order ID consistency
          final orderIdFields = ['id', 'orderId', 'order_id'];
          int orderIdCount = 0;
          for (final field in orderIdFields) {
            if (data.containsKey(field)) {
              orderIdCount++;
              result['id_field_coverage'][field] = (result['id_field_coverage'][field] ?? 0) + 1;
            }
          }

          if (orderIdCount == 0) {
            (result['issues'] as List<String>).add('Order ${doc.id}: Missing order ID fields');
          }

        } catch (e) {
          (result['issues'] as List<String>).add('Error processing order ${doc.id}: $e');
        }
      }

      // Determine status
      final userIdCoverage = result['orders_with_user_id'] / result['total_orders'];
      if (userIdCoverage < 0.9) {
        result['status'] = 'failed';
      } else if (userIdCoverage < 1.0 || (result['issues'] as List).isNotEmpty) {
        result['status'] = 'warning';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test address integration and display
  Future<Map<String, dynamic>> _testAddressIntegration() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'total_orders': 0,
        'orders_with_address': 0,
        'structured_addresses': 0,
        'string_addresses': 0,
        'address_field_coverage': <String, int>{},
        'issues': <String>[],
      };

      // Get sample orders
      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(30)
          .get();

      result['total_orders'] = ordersSnapshot.docs.length;

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();

          // Check for address fields
          final hasShippingAddress = data.containsKey('shipping_address');
          final hasShippingAddressAlt = data.containsKey('shippingAddress');

          if (hasShippingAddress || hasShippingAddressAlt) {
            result['orders_with_address'] = (result['orders_with_address'] as int) + 1;

            // Check address format
            final addressData = data['shipping_address'] ?? data['shippingAddress'];

            if (addressData is Map<String, dynamic>) {
              result['structured_addresses'] = (result['structured_addresses'] as int) + 1;

              // Check address fields
              final addressFields = ['receiver', 'full_name', 'address_line_1', 'city', 'state', 'pincode', 'phone'];
              for (final field in addressFields) {
                if (addressData.containsKey(field) && addressData[field] != null) {
                  result['address_field_coverage'][field] = (result['address_field_coverage'][field] ?? 0) + 1;
                }
              }
            } else if (addressData is String) {
              result['string_addresses'] = (result['string_addresses'] as int) + 1;
            }
          } else {
            (result['issues'] as List<String>).add('Order ${doc.id}: Missing shipping address');
          }

        } catch (e) {
          (result['issues'] as List<String>).add('Error processing address for order ${doc.id}: $e');
        }
      }

      // Determine status
      final addressCoverage = result['orders_with_address'] / result['total_orders'];
      if (addressCoverage < 0.7) {
        result['status'] = 'failed';
      } else if (addressCoverage < 0.9 || (result['issues'] as List).isNotEmpty) {
        result['status'] = 'warning';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test complete order lifecycle across both apps
  Future<Map<String, dynamic>> _testOrderLifecycle() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'lifecycle_stages': <String, int>{},
        'status_transitions': <String, int>{},
        'issues': <String>[],
      };

      // Get orders and analyze their lifecycle stages
      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(50)
          .get();

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();
          final status = data['order_status'] ?? data['status'] ?? 'unknown';

          // Count lifecycle stages
          result['lifecycle_stages'][status] = (result['lifecycle_stages'][status] ?? 0) + 1;

          // Check for required fields at each stage
          switch (status) {
            case 'pending':
              if (!data.containsKey('user_id') && !data.containsKey('userId')) {
                (result['issues'] as List<String>).add('Pending order ${doc.id}: Missing user ID');
              }
              break;
            case 'processing':
              if (!data.containsKey('order_date') && !data.containsKey('orderDate')) {
                (result['issues'] as List<String>).add('Processing order ${doc.id}: Missing order date');
              }
              break;
            case 'shipped':
              // Should have shipping info
              if (!data.containsKey('shipping_address') && !data.containsKey('shippingAddress')) {
                (result['issues'] as List<String>).add('Shipped order ${doc.id}: Missing shipping address');
              }
              break;
            case 'delivered':
              // Should have delivery confirmation
              if (!data.containsKey('delivered_date') && !data.containsKey('deliveredDate')) {
                (result['issues'] as List<String>).add('Delivered order ${doc.id}: Missing delivery date');
              }
              break;
          }

          // Check for status transition timestamps
          if (data.containsKey('updated_at') || data.containsKey('updatedAt')) {
            result['status_transitions']['with_timestamps'] = (result['status_transitions']['with_timestamps'] ?? 0) + 1;
          } else {
            result['status_transitions']['without_timestamps'] = (result['status_transitions']['without_timestamps'] ?? 0) + 1;
          }

        } catch (e) {
          (result['issues'] as List<String>).add('Error analyzing lifecycle for order ${doc.id}: $e');
        }
      }

      // Determine status
      if ((result['issues'] as List).length > 10) {
        result['status'] = 'failed';
      } else if ((result['issues'] as List).isNotEmpty) {
        result['status'] = 'warning';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test order data parsing with user app format
  Future<Map<String, dynamic>> _testOrderDataParsing() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'total_orders_tested': 0,
        'successfully_parsed': 0,
        'parsing_errors': 0,
        'missing_fields': <String, int>{},
        'issues': <String>[],
      };

      // Test parsing with user app collections
      final usersSnapshot = await _firestore.collection('users').limit(5).get();

      for (final userDoc in usersSnapshot.docs) {
        try {
          final userOrdersSnapshot = await _firestore
              .collection('users')
              .doc(userDoc.id)
              .collection('ordered_products')
              .limit(3)
              .get();

          for (final orderDoc in userOrdersSnapshot.docs) {
            result['total_orders_tested'] = (result['total_orders_tested'] as int) + 1;

            try {
              final data = orderDoc.data();

              // Test OrderModel parsing
              final order = OrderModel.fromMap(data, orderDoc.id);
              result['successfully_parsed'] = (result['successfully_parsed'] as int) + 1;

              // Check for critical fields
              if (order.userId.isEmpty) {
                result['missing_fields']['userId'] = (result['missing_fields']['userId'] ?? 0) + 1;
              }
              if (order.totalAmount == 0) {
                result['missing_fields']['totalAmount'] = (result['missing_fields']['totalAmount'] ?? 0) + 1;
              }
              if (order.items.isEmpty) {
                result['missing_fields']['items'] = (result['missing_fields']['items'] ?? 0) + 1;
              }
              if (order.shippingAddress == null || order.shippingAddress!.isEmpty) {
                result['missing_fields']['shippingAddress'] = (result['missing_fields']['shippingAddress'] ?? 0) + 1;
              }
              if (order.customerName == null || order.customerName!.isEmpty) {
                result['missing_fields']['customerName'] = (result['missing_fields']['customerName'] ?? 0) + 1;
              }

            } catch (parseError) {
              result['parsing_errors'] = (result['parsing_errors'] as int) + 1;
              (result['issues'] as List<String>).add('Parse error for order ${orderDoc.id}: $parseError');
            }
          }
        } catch (userError) {
          (result['issues'] as List<String>).add('Error accessing orders for user ${userDoc.id}: $userError');
        }
      }

      // Determine status
      final parseSuccessRate = result['successfully_parsed'] / result['total_orders_tested'];
      if (parseSuccessRate < 0.8) {
        result['status'] = 'failed';
      } else if (parseSuccessRate < 1.0 || (result['issues'] as List).isNotEmpty) {
        result['status'] = 'warning';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test real-time data verification with actual user app orders
  Future<Map<String, dynamic>> _testRealTimeDataVerification() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'total_users_checked': 0,
        'users_with_orders': 0,
        'total_orders_found': 0,
        'orders_with_complete_data': 0,
        'data_completeness': <String, int>{},
        'issues': <String>[],
      };

      // Check actual user app data
      final usersSnapshot = await _firestore.collection('users').limit(10).get();
      result['total_users_checked'] = usersSnapshot.docs.length;

      for (final userDoc in usersSnapshot.docs) {
        try {
          final userOrdersSnapshot = await _firestore
              .collection('users')
              .doc(userDoc.id)
              .collection('ordered_products')
              .get();

          if (userOrdersSnapshot.docs.isNotEmpty) {
            result['users_with_orders'] = (result['users_with_orders'] as int) + 1;
            result['total_orders_found'] = (result['total_orders_found'] as int) + userOrdersSnapshot.docs.length;

            for (final orderDoc in userOrdersSnapshot.docs) {
              final data = orderDoc.data();
              bool isComplete = true;

              // Check critical fields
              final checks = {
                'userId': data['userId'] != null && data['userId'].toString().isNotEmpty,
                'amount': data['amount'] != null && data['amount'] != 0,
                'items': data['items'] is List && (data['items'] as List).isNotEmpty,
                'customerInfo': data['customerInfo'] is Map && (data['customerInfo'] as Map).containsKey('name'),
                'shippingAddress': data['shippingAddress'] is Map,
                'status': data['status'] != null && data['status'].toString().isNotEmpty,
                'createdAt': data['createdAt'] != null,
              };

              for (final entry in checks.entries) {
                if (entry.value) {
                  result['data_completeness'][entry.key] = (result['data_completeness'][entry.key] ?? 0) + 1;
                } else {
                  isComplete = false;
                  (result['issues'] as List<String>).add('Order ${orderDoc.id}: Missing ${entry.key}');
                }
              }

              if (isComplete) {
                result['orders_with_complete_data'] = (result['orders_with_complete_data'] as int) + 1;
              }
            }
          }
        } catch (userError) {
          (result['issues'] as List<String>).add('Error checking user ${userDoc.id}: $userError');
        }
      }

      // Determine status
      final completenessRate = result['total_orders_found'] > 0
          ? result['orders_with_complete_data'] / result['total_orders_found']
          : 0.0;

      if (completenessRate < 0.7) {
        result['status'] = 'failed';
      } else if (completenessRate < 0.9 || (result['issues'] as List).isNotEmpty) {
        result['status'] = 'warning';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test sample data parsing with user app format
  Map<String, dynamic> _testSampleDataParsing() {
    try {
      // Call the OrderModel test method
      OrderModel.testUserAppDataParsing();

      return {
        'status': 'passed',
        'message': 'Sample data parsing test completed - check debug logs for details',
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test data structure compatibility between apps
  Future<Map<String, dynamic>> _testDataStructureCompatibility() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'total_orders': 0,
        'compatible_orders': 0,
        'incompatible_orders': 0,
        'issues': <String>[],
      };

      // Get sample orders from user app collection
      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(50)
          .get();

      result['total_orders'] = ordersSnapshot.docs.length;

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();
          
          // Try to parse with admin app OrderModel
          OrderModel.fromMap(data, doc.id);
          result['compatible_orders'] = (result['compatible_orders'] as int) + 1;
          
        } catch (e) {
          result['incompatible_orders'] = (result['incompatible_orders'] as int) + 1;
          (result['issues'] as List<String>).add('Order ${doc.id}: $e');
        }
      }

      if (result['incompatible_orders'] > 0) {
        result['status'] = 'failed';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test order parsing functionality
  Future<Map<String, dynamic>> _testOrderParsing() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'single_item_orders': 0,
        'multi_item_orders': 0,
        'parsing_errors': 0,
        'field_coverage': <String, int>{},
        'issues': <String>[],
      };

      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(30)
          .get();

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();
          final order = OrderModel.fromMap(data, doc.id);
          
          // Check if it's single or multi-item
          if (order.items.length == 1) {
            result['single_item_orders'] = (result['single_item_orders'] as int) + 1;
          } else if (order.items.length > 1) {
            result['multi_item_orders'] = (result['multi_item_orders'] as int) + 1;
          }

          // Check field coverage
          final fields = [
            'customerName', 'customerEmail', 'customerPhone',
            'paymentId', 'razorpayOrderId', 'isPaymentVerified',
            'shippingAddress', 'trackingNumber'
          ];

          for (final field in fields) {
            if (_hasField(order, field)) {
              result['field_coverage'][field] = (result['field_coverage'][field] ?? 0) + 1;
            }
          }

        } catch (e) {
          result['parsing_errors'] = (result['parsing_errors'] as int) + 1;
          (result['issues'] as List<String>).add('Parsing error for ${doc.id}: $e');
        }
      }

      if (result['parsing_errors'] > 0) {
        result['status'] = 'failed';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test payment integration functionality
  Future<Map<String, dynamic>> _testPaymentIntegration() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'total_payments': 0,
        'razorpay_payments': 0,
        'verified_payments': 0,
        'unverified_payments': 0,
        'payment_fields_present': 0,
        'issues': <String>[],
      };

      final payments = await _paymentService.getAllPayments();
      result['total_payments'] = payments.length;

      for (final payment in payments) {
        if (payment['paymentMethod']?.toString().toLowerCase() == 'razorpay') {
          result['razorpay_payments'] = (result['razorpay_payments'] as int) + 1;
          
          if (payment['isVerified'] == true) {
            result['verified_payments'] = (result['verified_payments'] as int) + 1;
          } else {
            result['unverified_payments'] = (result['unverified_payments'] as int) + 1;
          }

          // Check if payment fields are present
          if (payment['paymentId'] != null && payment['razorpayOrderId'] != null) {
            result['payment_fields_present'] = (result['payment_fields_present'] as int) + 1;
          }
        }
      }

      // Check if payment service is working
      try {
        final stats = await _paymentService.getPaymentStatistics();
        if (stats.containsKey('error')) {
          (result['issues'] as List<String>).add('Payment statistics error: ${stats['error']}');
          result['status'] = 'warning';
        }
      } catch (e) {
        (result['issues'] as List<String>).add('Payment service error: $e');
        result['status'] = 'failed';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test status update synchronization
  Future<Map<String, dynamic>> _testStatusUpdateSync() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'test_performed': false,
        'sync_successful': false,
        'issues': <String>[],
      };

      // Find a test order to update (don't actually update, just verify the mechanism)
      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(1)
          .get();

      if (ordersSnapshot.docs.isNotEmpty) {
        final testOrder = ordersSnapshot.docs.first;
        final data = testOrder.data();
        
        // Verify that the order has the necessary fields for status updates
        final hasUserId = data.containsKey('user_id') || data.containsKey('userId');
        final hasStatus = data.containsKey('order_status') || data.containsKey('status');
        
        if (hasUserId && hasStatus) {
          result['test_performed'] = true;
          result['sync_successful'] = true;
        } else {
          (result['issues'] as List<String>).add('Test order missing required fields for status updates');
          result['status'] = 'warning';
        }
      } else {
        (result['issues'] as List<String>).add('No orders available for status update testing');
        result['status'] = 'warning';
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test user data consistency
  Future<Map<String, dynamic>> _testUserDataConsistency() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'orders_with_user_data': 0,
        'orders_missing_user_data': 0,
        'user_id_consistency': 0,
        'customer_data_coverage': <String, int>{},
        'issues': <String>[],
      };

      final ordersSnapshot = await _firestore
          .collection(ordersCollection)
          .limit(30)
          .get();

      for (final doc in ordersSnapshot.docs) {
        final data = doc.data();
        
        // Check user ID consistency
        final hasUserId = data.containsKey('user_id') || data.containsKey('userId');
        if (hasUserId) {
          result['user_id_consistency'] = (result['user_id_consistency'] as int) + 1;
        }

        // Check customer data
        final customerFields = ['customer_name', 'customerName', 'customer_email', 'customerEmail'];
        bool hasCustomerData = false;
        
        for (final field in customerFields) {
          if (data.containsKey(field) && data[field] != null && data[field].toString().isNotEmpty) {
            hasCustomerData = true;
            result['customer_data_coverage'][field] = (result['customer_data_coverage'][field] ?? 0) + 1;
          }
        }

        if (hasCustomerData) {
          result['orders_with_user_data'] = (result['orders_with_user_data'] as int) + 1;
        } else {
          result['orders_missing_user_data'] = (result['orders_missing_user_data'] as int) + 1;
        }
      }

      if (result['orders_missing_user_data'] > result['orders_with_user_data']) {
        result['status'] = 'warning';
        (result['issues'] as List<String>).add('Many orders missing customer data');
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Test collection structure
  Future<Map<String, dynamic>> _testCollectionStructure() async {
    try {
      final result = <String, dynamic>{
        'status': 'passed',
        'collections_found': <String>[],
        'collection_counts': <String, int>{},
        'issues': <String>[],
      };

      // Check main collections
      final collections = ['orders', 'ordered_products', 'users', 'products', 'admin_orders'];
      
      for (final collection in collections) {
        try {
          final snapshot = await _firestore.collection(collection).limit(1).get();
          result['collections_found'].add(collection);
          
          final countSnapshot = await _firestore.collection(collection).count().get();
          result['collection_counts'][collection] = countSnapshot.count ?? 0;
          
        } catch (e) {
          (result['issues'] as List<String>).add('Collection $collection not accessible: $e');
        }
      }

      // Verify that we have the essential collections
      final essentialCollections = ['orders', 'users', 'products'];
      for (final essential in essentialCollections) {
        if (!(result['collections_found'] as List).contains(essential)) {
          result['status'] = 'failed';
          (result['issues'] as List<String>).add('Essential collection $essential not found');
        }
      }

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// Generate test summary
  Map<String, dynamic> _generateTestSummary(Map<String, dynamic> tests) {
    int passed = 0;
    int failed = 0;
    int warnings = 0;
    int errors = 0;

    for (final test in tests.values) {
      if (test is Map<String, dynamic>) {
        switch (test['status']) {
          case 'passed':
            passed++;
            break;
          case 'failed':
            failed++;
            break;
          case 'warning':
            warnings++;
            break;
          case 'error':
            errors++;
            break;
        }
      }
    }

    String overallStatus = 'passed';
    if (errors > 0) {
      overallStatus = 'error';
    } else if (failed > 0) {
      overallStatus = 'failed';
    } else if (warnings > 0) {
      overallStatus = 'warning';
    }

    return {
      'total_tests': tests.length,
      'passed': passed,
      'failed': failed,
      'warnings': warnings,
      'errors': errors,
      'overall_status': overallStatus,
      'success_rate': tests.isNotEmpty ? (passed / tests.length * 100).toStringAsFixed(1) : '0',
    };
  }

  /// Generate recommendations based on test results
  List<String> _generateRecommendations(Map<String, dynamic> tests) {
    final recommendations = <String>[];

    // Check data structure test
    if (tests['data_structure']?['status'] == 'failed') {
      recommendations.add('Run data synchronization to fix incompatible order structures');
    }

    // Check payment integration
    if (tests['payment_integration']?['unverified_payments'] > 0) {
      recommendations.add('Run bulk payment verification to verify unverified payments');
    }

    // Check user data consistency
    if (tests['user_data']?['orders_missing_user_data'] > 0) {
      recommendations.add('Run data synchronization to extract missing customer data');
    }

    // Check collection structure
    if (tests['collection_structure']?['status'] == 'failed') {
      recommendations.add('Verify Firebase security rules and collection permissions');
    }

    // General recommendations
    if (recommendations.isEmpty) {
      recommendations.add('All tests passed! Cross-app integration is working well.');
    } else {
      recommendations.add('Use the Data Sync page to fix most of these issues automatically');
    }

    return recommendations;
  }

  /// Helper method to check if order has a specific field
  bool _hasField(OrderModel order, String field) {
    switch (field) {
      case 'customerName':
        return order.customerName != null && order.customerName!.isNotEmpty;
      case 'customerEmail':
        return order.customerEmail != null && order.customerEmail!.isNotEmpty;
      case 'customerPhone':
        return order.customerPhone != null && order.customerPhone!.isNotEmpty;
      case 'paymentId':
        return order.paymentId != null && order.paymentId!.isNotEmpty;
      case 'razorpayOrderId':
        return order.razorpayOrderId != null && order.razorpayOrderId!.isNotEmpty;
      case 'isPaymentVerified':
        return true; // This field always exists (defaults to false)
      case 'shippingAddress':
        return order.shippingAddress != null && order.shippingAddress!.isNotEmpty;
      case 'trackingNumber':
        return order.trackingNumber != null && order.trackingNumber!.isNotEmpty;
      default:
        return false;
    }
  }
}
