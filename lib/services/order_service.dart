import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import '../models/order_model.dart';

class OrderService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Singleton pattern
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  // Get all orders from both collection structures
  Future<List<OrderModel>> getAllOrders() async {
    try {
      final List<OrderModel> allOrders = [];
      final Set<String> processedOrderIds = {}; // Prevent duplicates

      // Method 1: Get orders from root collection (User app format)
      try {
        debugPrint('Fetching orders from root collection: $ordersCollection');
        final rootOrdersSnapshot = await _firestore.collection(ordersCollection).get();

        for (final doc in rootOrdersSnapshot.docs) {
          try {
            if (!processedOrderIds.contains(doc.id)) {
              final order = OrderModel.fromMap(doc.data(), doc.id);
              allOrders.add(order);
              processedOrderIds.add(doc.id);
            }
          } catch (docError) {
            debugPrint('Error parsing order ${doc.id} from root collection: $docError');
          }
        }
        debugPrint('Found ${rootOrdersSnapshot.docs.length} orders in root collection');
      } catch (rootOrdersError) {
        debugPrint('Error getting orders from root collection: $rootOrdersError');
      }

      // Method 2: Get orders from user subcollections (Admin app format)
      try {
        debugPrint('Fetching orders from user subcollections');
        final usersSnapshot = await _firestore.collection(usersCollection).get();

        for (final userDoc in usersSnapshot.docs) {
          try {
            if (userDoc.id.isEmpty) {
              debugPrint('Skipping user with empty ID');
              continue;
            }

            final ordersSnapshot = await _firestore
                .collection(usersCollection)
                .doc(userDoc.id)
                .collection(ordersCollection)
                .get();

            for (final orderDoc in ordersSnapshot.docs) {
              try {
                if (!processedOrderIds.contains(orderDoc.id)) {
                  final order = OrderModel.fromMap(orderDoc.data(), orderDoc.id);
                  allOrders.add(order);
                  processedOrderIds.add(orderDoc.id);
                }
              } catch (docError) {
                debugPrint('Error parsing order ${orderDoc.id} from user ${userDoc.id}: $docError');
              }
            }
          } catch (userError) {
            debugPrint('Error getting orders for user ${userDoc.id}: $userError');
          }
        }
      } catch (userCollectionError) {
        debugPrint('Error getting orders from user collections: $userCollectionError');
      }

      // Sort orders by date (newest first)
      allOrders.sort((a, b) {
        try {
          // orderDate is always a String in OrderModel, parse it to DateTime
          DateTime dateA = DateTime.tryParse(a.orderDate) ?? DateTime.now();
          DateTime dateB = DateTime.tryParse(b.orderDate) ?? DateTime.now();
          return dateB.compareTo(dateA);
        } catch (e) {
          return 0; // Keep original order if comparison fails
        }
      });

      debugPrint('Loaded ${allOrders.length} unique orders in total');
      return allOrders;
    } catch (e) {
      debugPrint('Error getting all orders: $e');
      rethrow;
    }
  }

  // Get orders by status
  Future<List<OrderModel>> getOrdersByStatus(OrderStatus status) async {
    try {
      final allOrders = await getAllOrders();
      return allOrders.where((order) => order.status == status).toList();
    } catch (e) {
      debugPrint('Error getting orders by status: $e');
      rethrow;
    }
  }

  // Get orders by user (alias for getUserOrders)
  Future<List<OrderModel>> getOrdersByUser(String userId) async {
    return getUserOrders(userId);
  }

  // Get orders by user from both collection structures
  Future<List<OrderModel>> getUserOrders(String userId) async {
    try {
      // Validate parameters
      if (userId.isEmpty) {
        throw Exception('User ID cannot be empty');
      }

      debugPrint('Getting orders by user: userId=$userId');
      final List<OrderModel> orders = [];
      final Set<String> processedOrderIds = {}; // Prevent duplicates

      // Method 1: Get from user subcollection (Admin app format)
      try {
        final userOrdersSnapshot = await _firestore
            .collection(usersCollection)
            .doc(userId)
            .collection(ordersCollection)
            .get();

        for (final doc in userOrdersSnapshot.docs) {
          try {
            if (!processedOrderIds.contains(doc.id)) {
              final order = OrderModel.fromMap(doc.data(), doc.id);
              orders.add(order);
              processedOrderIds.add(doc.id);
            }
          } catch (docError) {
            debugPrint('Error parsing order ${doc.id} from user subcollection: $docError');
          }
        }
        debugPrint('Found ${userOrdersSnapshot.docs.length} orders in user subcollection');
      } catch (userSubError) {
        debugPrint('Error getting orders from user subcollection: $userSubError');
      }

      // Method 2: Get from root collection with user filter (User app format)
      try {
        final rootOrdersSnapshot = await _firestore
            .collection(ordersCollection)
            .where('userId', isEqualTo: userId)
            .get();

        for (final doc in rootOrdersSnapshot.docs) {
          try {
            if (!processedOrderIds.contains(doc.id)) {
              final order = OrderModel.fromMap(doc.data(), doc.id);
              orders.add(order);
              processedOrderIds.add(doc.id);
            }
          } catch (docError) {
            debugPrint('Error parsing order ${doc.id} from root collection: $docError');
          }
        }
        debugPrint('Found ${rootOrdersSnapshot.docs.length} orders in root collection for user');
      } catch (rootError) {
        debugPrint('Error getting orders from root collection: $rootError');
      }

      // Also try with user_id field (alternative field name)
      try {
        final rootOrdersSnapshot2 = await _firestore
            .collection(ordersCollection)
            .where('user_id', isEqualTo: userId)
            .get();

        for (final doc in rootOrdersSnapshot2.docs) {
          try {
            if (!processedOrderIds.contains(doc.id)) {
              final order = OrderModel.fromMap(doc.data(), doc.id);
              orders.add(order);
              processedOrderIds.add(doc.id);
            }
          } catch (docError) {
            debugPrint('Error parsing order ${doc.id} from root collection (user_id): $docError');
          }
        }
        debugPrint('Found ${rootOrdersSnapshot2.docs.length} orders in root collection with user_id field');
      } catch (rootError2) {
        debugPrint('Error getting orders from root collection with user_id: $rootError2');
      }

      debugPrint('Found ${orders.length} total unique orders for user $userId');
      return orders;
    } catch (e) {
      debugPrint('Error getting orders by user: $e');
      rethrow;
    }
  }

  // Get order by ID
  Future<OrderModel?> getOrderById(String userId, String orderId) async {
    try {
      // Validate parameters
      if (userId.isEmpty) {
        throw Exception('User ID cannot be empty');
      }

      if (orderId.isEmpty) {
        throw Exception('Order ID cannot be empty');
      }

      debugPrint('Getting order by ID: userId=$userId, orderId=$orderId');

      final docSnapshot = await _firestore
          .collection(usersCollection)
          .doc(userId)
          .collection(ordersCollection)
          .doc(orderId)
          .get();

      if (docSnapshot.exists) {
        return OrderModel.fromMap(docSnapshot.data()!, docSnapshot.id);
      }

      debugPrint('Order not found');
      return null;
    } catch (e) {
      debugPrint('Error getting order by ID: $e');
      rethrow;
    }
  }

  // Update order status
  Future<void> updateOrderStatus(String userId, String orderId, OrderStatus newStatus) async {
    try {
      // Validate parameters
      if (userId.isEmpty) {
        throw Exception('User ID cannot be empty');
      }

      if (orderId.isEmpty) {
        throw Exception('Order ID cannot be empty');
      }

      debugPrint('Updating order status: userId=$userId, orderId=$orderId, newStatus=${newStatus.toString().split('.').last}');

      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .collection(ordersCollection)
          .doc(orderId)
          .update({
        'status': _orderStatusToString(newStatus),
        // Update timestamps based on status
        if (newStatus == OrderStatus.shipped) 'shippedDate': Timestamp.now(),
        if (newStatus == OrderStatus.delivered) 'deliveredDate': Timestamp.now(),
      });

      debugPrint('Order status updated successfully');
    } catch (e) {
      debugPrint('Error updating order status: $e');
      rethrow;
    }
  }

  // Update order tracking information
  Future<void> updateOrderTracking(String userId, String orderId, String trackingNumber) async {
    try {
      // Validate parameters
      if (userId.isEmpty) {
        throw Exception('User ID cannot be empty');
      }

      if (orderId.isEmpty) {
        throw Exception('Order ID cannot be empty');
      }

      debugPrint('Updating order tracking: userId=$userId, orderId=$orderId');

      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .collection(ordersCollection)
          .doc(orderId)
          .update({
        'trackingNumber': trackingNumber,
      });

      debugPrint('Order tracking updated successfully');
    } catch (e) {
      debugPrint('Error updating order tracking: $e');
      rethrow;
    }
  }

  // Update order payment status
  Future<void> updateOrderPaymentStatus(String userId, String orderId, bool isPaid) async {
    try {
      // Validate parameters
      if (userId.isEmpty) {
        throw Exception('User ID cannot be empty');
      }

      if (orderId.isEmpty) {
        throw Exception('Order ID cannot be empty');
      }

      debugPrint('Updating order payment status: userId=$userId, orderId=$orderId, isPaid=$isPaid');

      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .collection(ordersCollection)
          .doc(orderId)
          .update({
        'isPaid': isPaid,
      });

      debugPrint('Order payment status updated successfully');
    } catch (e) {
      debugPrint('Error updating order payment status: $e');
      rethrow;
    }
  }

  // Cancel order
  Future<void> cancelOrder(String userId, String orderId, String reason) async {
    try {
      // Validate parameters
      if (userId.isEmpty) {
        throw Exception('User ID cannot be empty');
      }

      if (orderId.isEmpty) {
        throw Exception('Order ID cannot be empty');
      }

      debugPrint('Cancelling order: userId=$userId, orderId=$orderId');

      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .collection(ordersCollection)
          .doc(orderId)
          .update({
        'status': 'cancelled',
        'cancelReason': reason,
      });

      debugPrint('Order cancelled successfully');
    } catch (e) {
      debugPrint('Error cancelling order: $e');
      rethrow;
    }
  }

  // Return order
  Future<void> returnOrder(String userId, String orderId, String reason) async {
    try {
      // Validate parameters
      if (userId.isEmpty) {
        throw Exception('User ID cannot be empty');
      }

      if (orderId.isEmpty) {
        throw Exception('Order ID cannot be empty');
      }

      debugPrint('Returning order: userId=$userId, orderId=$orderId');

      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .collection(ordersCollection)
          .doc(orderId)
          .update({
        'status': 'returned',
        'returnReason': reason,
      });

      debugPrint('Order returned successfully');
    } catch (e) {
      debugPrint('Error returning order: $e');
      rethrow;
    }
  }

  // Get user information by ID
  Future<Map<String, dynamic>?> getUserInfo(String userId) async {
    try {
      // Validate parameters
      if (userId.isEmpty) {
        debugPrint('Cannot get user info: User ID is empty');
        return null;
      }

      debugPrint('Getting user info for userId: $userId');

      final docSnapshot = await _firestore.collection(usersCollection).doc(userId).get();

      if (docSnapshot.exists) {
        return docSnapshot.data();
      }

      debugPrint('User not found');
      return null;
    } catch (e) {
      debugPrint('Error getting user info: $e');
      return null;
    }
  }

  // Helper method to convert OrderStatus to string
  String _orderStatusToString(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'pending';
      case OrderStatus.processing:
        return 'processing';
      case OrderStatus.shipped:
        return 'shipped';
      case OrderStatus.delivered:
        return 'delivered';
      case OrderStatus.cancelled:
        return 'cancelled';
      case OrderStatus.returned:
        return 'returned';
    }
  }

  Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final snapshot = await _firestore.collection(usersCollection).get();

      final users = snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'displayName': data['displayName'] ?? 'No Name',
          'email': data['email'] ?? 'No Email',
        };
      }).toList();

      // Sort users by display name
      users.sort((a, b) => (a['displayName'] as String).compareTo(b['displayName'] as String));

      debugPrint('Loaded ${users.length} users for selection');
      return users;
    } catch (e) {
      debugPrint('Error getting users: $e');
      return [];
    }
  }

  /// Fix an order with a missing user ID by assigning it to an existing user
  Future<bool> fixOrderWithMissingUserId(String orderId, String userId) async {
    try {
      // Validate parameters
      if (orderId.isEmpty) {
        debugPrint('Order ID cannot be empty');
        return false;
      }

      if (userId.isEmpty) {
        debugPrint('User ID cannot be empty');
        return false;
      }

      // Check if user exists
      final userDoc = await _firestore.collection(usersCollection).doc(userId).get();
      if (!userDoc.exists) {
        debugPrint('User $userId does not exist');
        return false;
      }

      // Find the order first
      OrderModel? order;
      DocumentReference? orderRef;
      String? sourceLocation;

      // Check in root orders collection
      try {
        final docSnapshot = await _firestore.collection(ordersCollection).doc(orderId).get();
        if (docSnapshot.exists) {
          order = OrderModel.fromMap(docSnapshot.data()!, docSnapshot.id);
          orderRef = docSnapshot.reference;
          sourceLocation = "root";
          debugPrint('Found order in root collection');
        }
      } catch (e) {
        debugPrint('Error checking root orders collection: $e');
      }

      // If not found, check in all users' orders collections
      if (order == null) {
        final usersSnapshot = await _firestore.collection(usersCollection).get();

        for (final userDoc in usersSnapshot.docs) {
          try {
            final docSnapshot = await _firestore
                .collection(usersCollection)
                .doc(userDoc.id)
                .collection(ordersCollection)
                .doc(orderId)
                .get();

            if (docSnapshot.exists) {
              order = OrderModel.fromMap(docSnapshot.data()!, docSnapshot.id);
              orderRef = docSnapshot.reference;
              sourceLocation = userDoc.id;
              debugPrint('Found order in user ${userDoc.id} collection');
              break;
            }
          } catch (e) {
            debugPrint('Error checking orders for user ${userDoc.id}: $e');
          }
        }
      }

      // If order is found and has an empty user ID, fix it
      if (order != null && order.userId.isEmpty && orderRef != null) {
        // Get the user's orders collection
        final userOrdersRef = _firestore
            .collection(usersCollection)
            .doc(userId)
            .collection(ordersCollection);

        // Get the full order data
        final orderData = await orderRef.get();
        if (!orderData.exists) {
          debugPrint('Order data not found');
          return false;
        }

        // Create a new order document in the user's collection
        final newOrderRef = userOrdersRef.doc(orderId);

        // Update the order data with the new user ID
        final updatedOrderData = orderData.data() as Map<String, dynamic>;
        updatedOrderData['userId'] = userId;

        // Create the new document
        await newOrderRef.set(updatedOrderData);
        debugPrint('Created new order document for user $userId');

        // Optionally delete the original document
        if (sourceLocation != null) {
          try {
            await orderRef.delete();
            debugPrint('Deleted original order document');
          } catch (e) {
            debugPrint('Error deleting original order: $e');
            // Continue even if delete fails
          }
        }

        debugPrint('Fixed order $orderId by assigning it to user $userId');
        return true;
      }

      if (order == null) {
        debugPrint('Order $orderId not found');
      } else if (order.userId.isNotEmpty) {
        debugPrint('Order $orderId already has a user ID: ${order.userId}');
      } else if (orderRef == null) {
        debugPrint('Order reference not found for $orderId');
      }

      return false;
    } catch (e) {
      debugPrint('Error fixing order with missing user ID: $e');
      return false;
    }
  }
  Future<int> fixAllOrdersWithMissingUserIds(String userId) async {
    try {
      // Validate user ID
      if (userId.isEmpty) {
        debugPrint('User ID cannot be empty');
        return 0;
      }

      // Check if user exists
      final userDoc = await _firestore.collection(usersCollection).doc(userId).get();
      if (!userDoc.exists) {
        debugPrint('User $userId does not exist');
        return 0;
      }

      int fixedCount = 0;
      final allOrders = await getAllOrders();

      for (final order in allOrders) {
        if (order.userId.isEmpty) {
          final success = await fixOrderWithMissingUserId(order.id, userId);
          if (success) {
            fixedCount++;
          }
        }
      }

      debugPrint('Fixed $fixedCount orders with missing user IDs by assigning them to user $userId');
      return fixedCount;
    } catch (e) {
      debugPrint('Error fixing all orders with missing user IDs: $e');
      return 0;
    }
  }
}
