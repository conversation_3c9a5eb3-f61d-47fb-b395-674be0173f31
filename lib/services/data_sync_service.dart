import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import '../models/order_model.dart';

class DataSyncService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Singleton pattern
  static final DataSyncService _instance = DataSyncService._internal();
  factory DataSyncService() => _instance;
  DataSyncService._internal();

  /// Synchronize order data between user app and admin app formats
  Future<Map<String, dynamic>> synchronizeOrderData() async {
    try {
      debugPrint('Starting order data synchronization...');
      
      final results = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'orders_processed': 0,
        'orders_updated': 0,
        'errors': <String>[],
        'field_fixes': <String, int>{},
      };

      // Get all orders from the root collection (user app format)
      final ordersSnapshot = await _firestore.collection(ordersCollection).get();
      results['orders_processed'] = ordersSnapshot.docs.length;

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();
          final updates = <String, dynamic>{};
          bool needsUpdate = false;

          // Fix field name inconsistencies
          final fieldMappings = {
            // User ID fields
            'user_id': 'userId',
            'userId': 'user_id',
            
            // Order date fields
            'order_date': 'orderDate',
            'orderDate': 'order_date',
            
            // Order status fields
            'order_status': 'status',
            'status': 'order_status',
            
            // Payment fields
            'payment_id': 'paymentId',
            'paymentId': 'payment_id',
            'razorpay_order_id': 'razorpayOrderId',
            'razorpayOrderId': 'razorpay_order_id',
            'payment_signature': 'paymentSignature',
            'paymentSignature': 'payment_signature',
            'is_payment_verified': 'isPaymentVerified',
            'isPaymentVerified': 'is_payment_verified',
            'verified': 'isPaymentVerified',
            
            // Customer fields
            'customer_name': 'customerName',
            'customerName': 'customer_name',
            'customer_email': 'customerEmail',
            'customerEmail': 'customer_email',
            'customer_phone': 'customerPhone',
            'customerPhone': 'customer_phone',
            
            // Amount fields
            'total': 'totalAmount',
            'totalAmount': 'total',
            'amount': 'total',
          };

          // Apply field mappings
          for (final entry in fieldMappings.entries) {
            final sourceField = entry.key;
            final targetField = entry.value;
            
            if (data.containsKey(sourceField) && !data.containsKey(targetField)) {
              updates[targetField] = data[sourceField];
              needsUpdate = true;
              
              final fixKey = '$sourceField -> $targetField';
              results['field_fixes'][fixKey] = (results['field_fixes'][fixKey] ?? 0) + 1;
            }
          }

          // Extract customer data from nested objects
          if (data.containsKey('customer') && data['customer'] is Map) {
            final customerData = data['customer'] as Map<String, dynamic>;
            
            if (!data.containsKey('customer_name') && customerData.containsKey('name')) {
              updates['customer_name'] = customerData['name'];
              updates['customerName'] = customerData['name'];
              needsUpdate = true;
              results['field_fixes']['customer.name -> customer_name'] = 
                  (results['field_fixes']['customer.name -> customer_name'] ?? 0) + 1;
            }
            
            if (!data.containsKey('customer_email') && customerData.containsKey('email')) {
              updates['customer_email'] = customerData['email'];
              updates['customerEmail'] = customerData['email'];
              needsUpdate = true;
              results['field_fixes']['customer.email -> customer_email'] = 
                  (results['field_fixes']['customer.email -> customer_email'] ?? 0) + 1;
            }
            
            if (!data.containsKey('customer_phone') && customerData.containsKey('phone')) {
              updates['customer_phone'] = customerData['phone'];
              updates['customerPhone'] = customerData['phone'];
              needsUpdate = true;
              results['field_fixes']['customer.phone -> customer_phone'] = 
                  (results['field_fixes']['customer.phone -> customer_phone'] ?? 0) + 1;
            }
          }

          // Extract customer name from shipping address if missing
          if (!data.containsKey('customer_name') && 
              data.containsKey('shipping_address') && 
              data['shipping_address'] is Map) {
            final shippingData = data['shipping_address'] as Map<String, dynamic>;
            final receiverName = shippingData['receiver'] ?? shippingData['full_name'];
            
            if (receiverName != null && receiverName.toString().isNotEmpty) {
              updates['customer_name'] = receiverName;
              updates['customerName'] = receiverName;
              needsUpdate = true;
              results['field_fixes']['shipping_address.receiver -> customer_name'] = 
                  (results['field_fixes']['shipping_address.receiver -> customer_name'] ?? 0) + 1;
            }
          }

          // Ensure order items are in both formats
          if (data.containsKey('order_items') && !data.containsKey('items')) {
            updates['items'] = data['order_items'];
            needsUpdate = true;
            results['field_fixes']['order_items -> items'] = 
                (results['field_fixes']['order_items -> items'] ?? 0) + 1;
          } else if (data.containsKey('items') && !data.containsKey('order_items')) {
            updates['order_items'] = data['items'];
            needsUpdate = true;
            results['field_fixes']['items -> order_items'] = 
                (results['field_fixes']['items -> order_items'] ?? 0) + 1;
          }

          // Ensure timestamps are properly formatted
          if (data.containsKey('order_date') && data['order_date'] is! Timestamp) {
            try {
              final dateStr = data['order_date'].toString();
              final date = DateTime.parse(dateStr);
              updates['order_date'] = Timestamp.fromDate(date);
              updates['orderDate'] = Timestamp.fromDate(date);
              needsUpdate = true;
              results['field_fixes']['order_date timestamp fix'] = 
                  (results['field_fixes']['order_date timestamp fix'] ?? 0) + 1;
            } catch (e) {
              debugPrint('Error parsing order date for ${doc.id}: $e');
            }
          }

          // Apply updates if needed
          if (needsUpdate) {
            await doc.reference.update(updates);
            results['orders_updated'] = (results['orders_updated'] as int) + 1;
            debugPrint('Updated order ${doc.id} with ${updates.length} field fixes');
          }

        } catch (e) {
          final error = 'Error processing order ${doc.id}: $e';
          debugPrint(error);
          (results['errors'] as List<String>).add(error);
        }
      }

      debugPrint('Order data synchronization completed');
      return results;
    } catch (e) {
      debugPrint('Error in order data synchronization: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Verify data consistency between user and admin apps
  Future<Map<String, dynamic>> verifyDataConsistency() async {
    try {
      debugPrint('Starting data consistency verification...');
      
      final results = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'total_orders': 0,
        'parseable_orders': 0,
        'unparseable_orders': 0,
        'missing_fields': <String, int>{},
        'field_inconsistencies': <String, int>{},
        'recommendations': <String>[],
      };

      // Check orders in root collection
      final ordersSnapshot = await _firestore.collection(ordersCollection).get();
      results['total_orders'] = ordersSnapshot.docs.length;

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();
          
          // Try to parse with enhanced OrderModel
          OrderModel.fromMap(data, doc.id);
          results['parseable_orders'] = (results['parseable_orders'] as int) + 1;

          // Check for missing critical fields
          final criticalFields = [
            'user_id', 'userId',
            'order_date', 'orderDate',
            'order_status', 'status',
            'total', 'totalAmount', 'amount',
            'payment_method', 'paymentMethod',
          ];

          for (final field in criticalFields) {
            if (!data.containsKey(field)) {
              results['missing_fields'][field] = (results['missing_fields'][field] ?? 0) + 1;
            }
          }

          // Check for field inconsistencies
          final fieldPairs = [
            ['user_id', 'userId'],
            ['order_date', 'orderDate'],
            ['order_status', 'status'],
            ['total', 'totalAmount'],
            ['customer_name', 'customerName'],
            ['payment_id', 'paymentId'],
          ];

          for (final pair in fieldPairs) {
            final field1 = pair[0];
            final field2 = pair[1];
            
            if (data.containsKey(field1) && data.containsKey(field2)) {
              if (data[field1] != data[field2]) {
                final inconsistencyKey = '$field1 != $field2';
                results['field_inconsistencies'][inconsistencyKey] = 
                    (results['field_inconsistencies'][inconsistencyKey] ?? 0) + 1;
              }
            }
          }

        } catch (e) {
          results['unparseable_orders'] = (results['unparseable_orders'] as int) + 1;
          debugPrint('Error parsing order ${doc.id}: $e');
        }
      }

      // Generate recommendations
      final recommendations = results['recommendations'] as List<String>;
      
      if ((results['unparseable_orders'] as int) > 0) {
        recommendations.add('${results['unparseable_orders']} orders cannot be parsed - run data synchronization');
      }
      
      if ((results['missing_fields'] as Map).isNotEmpty) {
        recommendations.add('Missing fields detected - run data synchronization to fix');
      }
      
      if ((results['field_inconsistencies'] as Map).isNotEmpty) {
        recommendations.add('Field inconsistencies detected - manual review may be needed');
      }
      
      final parseableRate = results['total_orders'] > 0 
          ? (results['parseable_orders'] as int) / (results['total_orders'] as int) * 100
          : 0;
      
      if (parseableRate < 100) {
        recommendations.add('Data compatibility: ${parseableRate.toStringAsFixed(1)}% - improvement needed');
      } else {
        recommendations.add('Data compatibility: 100% - excellent!');
      }

      debugPrint('Data consistency verification completed');
      return results;
    } catch (e) {
      debugPrint('Error in data consistency verification: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Create admin-friendly copies of user orders
  Future<Map<String, dynamic>> createAdminOrderCopies() async {
    try {
      debugPrint('Creating admin-friendly order copies...');
      
      final results = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'orders_processed': 0,
        'admin_orders_created': 0,
        'admin_orders_updated': 0,
        'errors': <String>[],
      };

      // Get all orders from root collection
      final ordersSnapshot = await _firestore.collection(ordersCollection).get();
      results['orders_processed'] = ordersSnapshot.docs.length;

      for (final doc in ordersSnapshot.docs) {
        try {
          final data = doc.data();
          
          // Create admin-friendly order data
          final adminOrderData = <String, dynamic>{
            'order_id': doc.id,
            'user_id': data['user_id'] ?? data['userId'] ?? '',
            'customer_name': data['customer_name'] ?? data['customerName'] ?? 
                           (data['customer'] is Map ? data['customer']['name'] : null) ?? 'Unknown',
            'customer_email': data['customer_email'] ?? data['customerEmail'] ?? 
                            (data['customer'] is Map ? data['customer']['email'] : null) ?? '',
            'customer_phone': data['customer_phone'] ?? data['customerPhone'] ?? 
                            (data['customer'] is Map ? data['customer']['phone'] : null) ?? '',
            'total_amount': data['total'] ?? data['totalAmount'] ?? data['amount'] ?? 0,
            'payment_method': data['payment_method'] ?? data['paymentMethod'] ?? 'unknown',
            'order_status': data['order_status'] ?? data['status'] ?? 'unknown',
            'order_date': data['order_date'] ?? data['orderDate'] ?? FieldValue.serverTimestamp(),
            'payment_id': data['payment_id'] ?? data['paymentId'],
            'razorpay_order_id': data['razorpay_order_id'] ?? data['razorpayOrderId'] ?? data['order_id'],
            'is_payment_verified': data['is_payment_verified'] ?? data['isPaymentVerified'] ?? data['verified'] ?? false,
            'items_count': data['order_items'] is List ? (data['order_items'] as List).length : 
                          data['items'] is List ? (data['items'] as List).length : 1,
            'created_at': FieldValue.serverTimestamp(),
            'updated_at': FieldValue.serverTimestamp(),
            'source': 'user_app',
          };

          // Check if admin order already exists
          final adminOrderDoc = await _firestore
              .collection('admin_orders')
              .doc(doc.id)
              .get();

          if (adminOrderDoc.exists) {
            // Update existing admin order
            await adminOrderDoc.reference.update({
              ...adminOrderData,
              'updated_at': FieldValue.serverTimestamp(),
            });
            results['admin_orders_updated'] = (results['admin_orders_updated'] as int) + 1;
          } else {
            // Create new admin order
            await _firestore
                .collection('admin_orders')
                .doc(doc.id)
                .set(adminOrderData);
            results['admin_orders_created'] = (results['admin_orders_created'] as int) + 1;
          }

        } catch (e) {
          final error = 'Error processing order ${doc.id}: $e';
          debugPrint(error);
          (results['errors'] as List<String>).add(error);
        }
      }

      debugPrint('Admin order copies creation completed');
      return results;
    } catch (e) {
      debugPrint('Error creating admin order copies: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
