// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/foundation.dart';
//
// import '../constants/app_constants.dart';
// import '../models/order_model.dart';
//
//
// class AugmentedOrderService {
//   final FirebaseFirestore _firestore = FirebaseFirestore.instance;
//
//   // Singleton pattern
//   static final AugmentedOrderService _instance = AugmentedOrderService._internal();
//   factory AugmentedOrderService() => _instance;
//   AugmentedOrderService._internal();
//
//   /// Get all orders with enhanced error handling and data augmentation
//   Future<List<OrderModel>> getAllOrders() async {
//     try {
//       final List<OrderModel> allOrders = [];
//       final List<OrderModel> ordersWithIssues = [];
//
//       // Get all users
//       final usersSnapshot = await _firestore.collection(usersCollection).get();
//
//       // For each user, get their orders
//       for (final userDoc in usersSnapshot.docs) {
//         try {
//           if (userDoc.id.isEmpty) {
//             debugPrint('Skipping user with empty ID');
//             continue;
//           }
//
//           final ordersSnapshot = await _firestore
//               .collection(usersCollection)
//               .doc(userDoc.id)
//               .collection(ordersCollection)
//               .get();
//
//           final userOrders = ordersSnapshot.docs
//               .map((doc) => OrderModel.fromMap(doc.data(), doc.id))
//               .toList();
//
//           allOrders.addAll(userOrders);
//         } catch (userError) {
//           // Log error but continue with other users
//           debugPrint('Error getting orders for user ${userDoc.id}: $userError');
//         }
//       }
//
//       // Also get orders from the root orders collection (if it exists)
//       try {
//         final rootOrdersSnapshot = await _firestore.collection(ordersCollection).get();
//         final rootOrders = rootOrdersSnapshot.docs
//             .map((doc) => OrderModel.fromMap(doc.data(), doc.id))
//             .toList();
//         allOrders.addAll(rootOrders);
//       } catch (rootOrdersError) {
//         // Just log the error and continue
//         debugPrint('Error getting orders from root collection: $rootOrdersError');
//       }
//
//       // Check for orders with missing user IDs and add them to the issues list
//       for (final order in allOrders) {
//         if (order.userId.isEmpty) {
//           ordersWithIssues.add(order);
//         }
//       }
//
//       // Log information about orders with issues
//       if (ordersWithIssues.isNotEmpty) {
//         debugPrint('Found ${ordersWithIssues.length} orders with missing user IDs');
//         for (final order in ordersWithIssues) {
//           debugPrint('Order ${order.id} has no user ID, ordered on ${order.orderDate}');
//         }
//       }
//
//       // Sort orders by date (newest first)
//       allOrders.sort((a, b) => b.orderDate.compareTo(a.orderDate));
//
//       debugPrint('Loaded ${allOrders.length} orders in total');
//       return allOrders;
//     } catch (e) {
//       debugPrint('Error getting all orders: $e');
//       rethrow;
//     }
//   }
//
//   /// Get user information by ID with enhanced error handling
//   Future<Map<String, dynamic>?> getUserInfo(String userId) async {
//     try {
//       // Validate parameters
//       if (userId.isEmpty) {
//         debugPrint('Cannot get user info: User ID is empty');
//         return null;
//       }
//
//       debugPrint('Getting user info for userId: $userId');
//
//       final docSnapshot = await _firestore.collection(usersCollection).doc(userId).get();
//
//       if (docSnapshot.exists) {
//         return docSnapshot.data();
//       }
//
//       debugPrint('User not found');
//       return null;
//     } catch (e) {
//       debugPrint('Error getting user info: $e');
//       return null;
//     }
//   }
//
//   /// Get all users for selection
//   Future<List<Map<String, dynamic>>> getAllUsers() async {
//     try {
//       final snapshot = await _firestore.collection(usersCollection).get();
//
//       final users = snapshot.docs.map((doc) {
//         final data = doc.data();
//         return {
//           'id': doc.id,
//           'displayName': data['displayName'] ?? 'No Name',
//           'email': data['email'] ?? 'No Email',
//         };
//       }).toList();
//
//       // Sort users by display name
//       users.sort((a, b) => (a['displayName'] as String).compareTo(b['displayName'] as String));
//
//       debugPrint('Loaded ${users.length} users for selection');
//       return users;
//     } catch (e) {
//       debugPrint('Error getting users: $e');
//       return [];
//     }
//   }
//
//   /// Fix an order with a missing user ID by assigning it to an existing user
//   Future<bool> fixOrderWithMissingUserId(String orderId, String userId) async {
//     try {
//       // Validate parameters
//       if (orderId.isEmpty) {
//         debugPrint('Order ID cannot be empty');
//         return false;
//       }
//
//       if (userId.isEmpty) {
//         debugPrint('User ID cannot be empty');
//         return false;
//       }
//
//       // Check if user exists
//       final userDoc = await _firestore.collection(usersCollection).doc(userId).get();
//       if (!userDoc.exists) {
//         debugPrint('User $userId does not exist');
//         return false;
//       }
//
//       // Find the order first
//       OrderModel? order;
//       DocumentReference? orderRef;
//       String? sourceLocation;
//
//       // Check in root orders collection
//       try {
//         final docSnapshot = await _firestore.collection(ordersCollection).doc(orderId).get();
//         if (docSnapshot.exists) {
//           order = OrderModel.fromMap(docSnapshot.data()!, docSnapshot.id);
//           orderRef = docSnapshot.reference;
//           sourceLocation = "root";
//           debugPrint('Found order in root collection');
//         }
//       } catch (e) {
//         debugPrint('Error checking root orders collection: $e');
//       }
//
//       // If not found, check in all users' orders collections
//       if (order == null) {
//         final usersSnapshot = await _firestore.collection(usersCollection).get();
//
//         for (final userDoc in usersSnapshot.docs) {
//           try {
//             final docSnapshot = await _firestore
//                 .collection(usersCollection)
//                 .doc(userDoc.id)
//                 .collection(ordersCollection)
//                 .doc(orderId)
//                 .get();
//
//             if (docSnapshot.exists) {
//               order = OrderModel.fromMap(docSnapshot.data()!, docSnapshot.id);
//               orderRef = docSnapshot.reference;
//               sourceLocation = userDoc.id;
//               debugPrint('Found order in user ${userDoc.id} collection');
//               break;
//             }
//           } catch (e) {
//             debugPrint('Error checking orders for user ${userDoc.id}: $e');
//           }
//         }
//       }
//
//       // If order is found and has an empty user ID, fix it
//       if (order != null && order.userId.isEmpty && orderRef != null) {
//         // Get the user's orders collection
//         final userOrdersRef = _firestore
//             .collection(usersCollection)
//             .doc(userId)
//             .collection(ordersCollection);
//
//         // Get the full order data
//         final orderData = await orderRef.get();
//         if (!orderData.exists) {
//           debugPrint('Order data not found');
//           return false;
//         }
//
//         // Create a new order document in the user's collection
//         final newOrderRef = userOrdersRef.doc(orderId);
//
//         // Update the order data with the new user ID
//         final updatedOrderData = orderData.data() as Map<String, dynamic>;
//         updatedOrderData['userId'] = userId;
//
//         // Create the new document
//         await newOrderRef.set(updatedOrderData);
//         debugPrint('Created new order document for user $userId');
//
//         // Optionally delete the original document
//         if (sourceLocation != null) {
//           try {
//             await orderRef.delete();
//             debugPrint('Deleted original order document');
//           } catch (e) {
//             debugPrint('Error deleting original order: $e');
//             // Continue even if delete fails
//           }
//         }
//
//         debugPrint('Fixed order $orderId by assigning it to user $userId');
//         return true;
//       }
//
//       if (order == null) {
//         debugPrint('Order $orderId not found');
//       } else if (order.userId.isNotEmpty) {
//         debugPrint('Order $orderId already has a user ID: ${order.userId}');
//       } else if (orderRef == null) {
//         debugPrint('Order reference not found for $orderId');
//       }
//
//       return false;
//     } catch (e) {
//       debugPrint('Error fixing order with missing user ID: $e');
//       return false;
//     }
//   }
//
//   /// Fix all orders with missing user IDs by assigning them to a specific user
//   Future<int> fixAllOrdersWithMissingUserIds(String userId) async {
//     try {
//       // Validate user ID
//       if (userId.isEmpty) {
//         debugPrint('User ID cannot be empty');
//         return 0;
//       }
//
//       // Check if user exists
//       final userDoc = await _firestore.collection(usersCollection).doc(userId).get();
//       if (!userDoc.exists) {
//         debugPrint('User $userId does not exist');
//         return 0;
//       }
//
//       int fixedCount = 0;
//       final allOrders = await getAllOrders();
//
//       for (final order in allOrders) {
//         if (order.userId.isEmpty) {
//           final success = await fixOrderWithMissingUserId(order.id, userId);
//           if (success) {
//             fixedCount++;
//           }
//         }
//       }
//
//       debugPrint('Fixed $fixedCount orders with missing user IDs by assigning them to user $userId');
//       return fixedCount;
//     } catch (e) {
//       debugPrint('Error fixing all orders with missing user IDs: $e');
//       return 0;
//     }
//   }
// }
