import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import 'order_service.dart';

class DashboardService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final OrderService _orderService = OrderService();

  // Singleton pattern
  static final DashboardService _instance = DashboardService._internal();
  factory DashboardService() => _instance;
  DashboardService._internal();

  // Stream for total products count
  Stream<int> get totalProductsStream {
    return _firestore
        .collection(productsCollection)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Stream for total users count
  Stream<int> get totalUsersStream {
    return _firestore
        .collection(usersCollection)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Stream for total orders count
  Stream<int> get totalOrdersStream {
    // First, get a stream of all users
    return _firestore.collection(usersCollection).snapshots().asyncMap((usersSnapshot) async {
      int totalCount = 0;

      // For each user, get their orders
      for (final userDoc in usersSnapshot.docs) {
        final userOrdersSnapshot = await _firestore
            .collection(usersCollection)
            .doc(userDoc.id)
            .collection(ordersCollection)
            .get();

        totalCount += userOrdersSnapshot.docs.length;
      }

      // Also check the root orders collection
      final rootOrdersSnapshot = await _firestore.collection(ordersCollection).get();
      totalCount += rootOrdersSnapshot.docs.length;

      return totalCount;
    });
  }

  // Stream for pending orders count
  Stream<int> get pendingOrdersStream {
    // First, get a stream of all users
    return _firestore.collection(usersCollection).snapshots().asyncMap((usersSnapshot) async {
      int pendingCount = 0;

      // For each user, get their pending orders
      for (final userDoc in usersSnapshot.docs) {
        final userPendingOrdersSnapshot = await _firestore
            .collection(usersCollection)
            .doc(userDoc.id)
            .collection(ordersCollection)
            .where('status', isEqualTo: 'pending')
            .get();

        pendingCount += userPendingOrdersSnapshot.docs.length;
      }

      // Also check the root orders collection
      final rootPendingOrdersSnapshot = await _firestore
          .collection(ordersCollection)
          .where('status', isEqualTo: 'pending')
          .get();

      pendingCount += rootPendingOrdersSnapshot.docs.length;

      return pendingCount;
    });
  }

  // Stream for recent orders - optimized using OrderService
  Stream<List<Map<String, dynamic>>> get recentOrdersStream {
    return Stream.periodic(const Duration(seconds: 30)).asyncMap((_) async {
      try {
        final orders = await _orderService.getAllOrders();

        // Convert to the expected format and take only 5 most recent
        return orders.take(5).map((order) => {
          'id': order.id,
          'date': order.orderDate,
          'status': order.status.toString().split('.').last,
          'productId': order.productId,
          'userId': order.userId,
          'totalAmount': order.totalAmount,
          'items': order.items.length,
        }).toList();
      } catch (e) {
        debugPrint('Error getting recent orders: $e');
        return <Map<String, dynamic>>[];
      }
    });
  }

  // Stream for weekly sales data - optimized
  Stream<List<FlSpot>> get weeklySalesDataStream {
    return Stream.periodic(const Duration(minutes: 5)).asyncMap((_) async {
      try {
        final orders = await _orderService.getAllOrders();
        final now = DateTime.now();

        // Create a map to store sales by day (0 = today, 6 = 6 days ago)
        final Map<int, double> salesByDay = {};

        // Initialize the map with zeros for the last 7 days
        for (int i = 0; i < 7; i++) {
          salesByDay[i] = 0;
        }

        // Process each order
        for (final order in orders) {
          try {
            // Parse the order date
            final orderDate = DateTime.tryParse(order.orderDate);
            if (orderDate == null) continue;

            // Calculate days difference
            final difference = now.difference(orderDate).inDays;

            // Only consider orders from the last 7 days
            if (difference >= 0 && difference < 7) {
              // Add to the corresponding day
              salesByDay[difference] = (salesByDay[difference] ?? 0) + order.totalAmount.toDouble();
            }
          } catch (e) {
            debugPrint('Error processing order for sales data: $e');
          }
        }

        // Convert to FlSpot list (reverse order to show oldest to newest)
        final List<FlSpot> spots = [];
        for (int i = 6; i >= 0; i--) {
          spots.add(FlSpot((6 - i).toDouble(), salesByDay[i] ?? 0));
        }

        return spots;
      } catch (e) {
        debugPrint('Error getting weekly sales data: $e');
        // Return empty data on error
        return List.generate(7, (index) => FlSpot(index.toDouble(), 0));
      }
    });
  }

  // Get order status distribution - optimized
  Stream<Map<String, int>> get orderStatusDistributionStream {
    return Stream.periodic(const Duration(minutes: 2)).asyncMap((_) async {
      try {
        final orders = await _orderService.getAllOrders();

        final Map<String, int> distribution = {
          'pending': 0,
          'processing': 0,
          'shipped': 0,
          'delivered': 0,
          'cancelled': 0,
          'returned': 0,
        };

        // Process each order
        for (final order in orders) {
          final status = order.status.toString().split('.').last;
          distribution[status] = (distribution[status] ?? 0) + 1;
        }

        return distribution;
      } catch (e) {
        debugPrint('Error getting order status distribution: $e');
        return {
          'pending': 0,
          'processing': 0,
          'shipped': 0,
          'delivered': 0,
          'cancelled': 0,
          'returned': 0,
        };
      }
    });
  }

  // Get total revenue
  Future<double> getTotalRevenue() async {
    try {
      final orders = await _orderService.getAllOrders();
      return orders
          .where((order) => order.status.toString().split('.').last == 'delivered')
          .fold<double>(0.0, (total, order) => total + order.totalAmount.toDouble());
    } catch (e) {
      debugPrint('Error getting total revenue: $e');
      return 0.0;
    }
  }

  // Get monthly revenue
  Future<double> getMonthlyRevenue() async {
    try {
      final orders = await _orderService.getAllOrders();
      final now = DateTime.now();
      final monthStart = DateTime(now.year, now.month, 1);

      return orders
          .where((order) {
            final orderDate = DateTime.tryParse(order.orderDate);
            return orderDate != null &&
                   orderDate.isAfter(monthStart) &&
                   order.status.toString().split('.').last == 'delivered';
          })
          .fold<double>(0.0, (total, order) => total + order.totalAmount.toDouble());
    } catch (e) {
      debugPrint('Error getting monthly revenue: $e');
      return 0.0;
    }
  }
}
