import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

import '../constants/app_constants.dart';

class DashboardService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Singleton pattern
  static final DashboardService _instance = DashboardService._internal();
  factory DashboardService() => _instance;
  DashboardService._internal();

  // Stream for total products count
  Stream<int> get totalProductsStream {
    return _firestore
        .collection(productsCollection)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Stream for total users count
  Stream<int> get totalUsersStream {
    return _firestore
        .collection(usersCollection)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Stream for total orders count
  Stream<int> get totalOrdersStream {
    // First, get a stream of all users
    return _firestore.collection(usersCollection).snapshots().asyncMap((usersSnapshot) async {
      int totalCount = 0;

      // For each user, get their orders
      for (final userDoc in usersSnapshot.docs) {
        final userOrdersSnapshot = await _firestore
            .collection(usersCollection)
            .doc(userDoc.id)
            .collection(ordersCollection)
            .get();

        totalCount += userOrdersSnapshot.docs.length;
      }

      // Also check the root orders collection
      final rootOrdersSnapshot = await _firestore.collection(ordersCollection).get();
      totalCount += rootOrdersSnapshot.docs.length;

      return totalCount;
    });
  }

  // Stream for pending orders count
  Stream<int> get pendingOrdersStream {
    // First, get a stream of all users
    return _firestore.collection(usersCollection).snapshots().asyncMap((usersSnapshot) async {
      int pendingCount = 0;

      // For each user, get their pending orders
      for (final userDoc in usersSnapshot.docs) {
        final userPendingOrdersSnapshot = await _firestore
            .collection(usersCollection)
            .doc(userDoc.id)
            .collection(ordersCollection)
            .where('status', isEqualTo: 'pending')
            .get();

        pendingCount += userPendingOrdersSnapshot.docs.length;
      }

      // Also check the root orders collection
      final rootPendingOrdersSnapshot = await _firestore
          .collection(ordersCollection)
          .where('status', isEqualTo: 'pending')
          .get();

      pendingCount += rootPendingOrdersSnapshot.docs.length;

      return pendingCount;
    });
  }

  // Stream for recent orders
  Stream<List<Map<String, dynamic>>> get recentOrdersStream {
    // First, get a stream of all users
    return _firestore.collection(usersCollection).snapshots().asyncMap((usersSnapshot) async {
      List<Map<String, dynamic>> allOrders = [];

      // For each user, get their orders
      for (final userDoc in usersSnapshot.docs) {
        final userOrdersSnapshot = await _firestore
            .collection(usersCollection)
            .doc(userDoc.id)
            .collection(ordersCollection)
            .orderBy('order_date', descending: true)
            .get();

        final userOrders = userOrdersSnapshot.docs.map((doc) => {
          'id': doc.id,
          'date': doc.data()['order_date'] ?? '',
          'status': doc.data()['status'] ?? 'pending',
          'productId': doc.data()['product_uid'] ?? '',
          'userId': userDoc.id,
        }).toList();

        allOrders.addAll(userOrders);
      }

      // Also check the root orders collection
      final rootOrdersSnapshot = await _firestore
          .collection(ordersCollection)
          .orderBy('order_date', descending: true)
          .get();

      final rootOrders = rootOrdersSnapshot.docs.map((doc) => {
        'id': doc.id,
        'date': doc.data()['order_date'] ?? '',
        'status': doc.data()['status'] ?? 'pending',
        'productId': doc.data()['product_uid'] ?? '',
        'userId': doc.data()['userId'] ?? '',
      }).toList();

      allOrders.addAll(rootOrders);

      // Sort by date (newest first)
      allOrders.sort((a, b) => (b['date'] as String).compareTo(a['date'] as String));

      // Return only the 5 most recent orders
      return allOrders.take(5).toList();
    });
  }

  // Stream for weekly sales data
  Stream<List<FlSpot>> get weeklySalesDataStream {
    // First, get a stream of all users
    return _firestore.collection(usersCollection).snapshots().asyncMap((usersSnapshot) async {
      // Get the current date
      final now = DateTime.now();

      // Create a map to store sales by day
      final Map<int, double> salesByDay = {};

      // Initialize the map with zeros for the last 7 days
      for (int i = 0; i < 7; i++) {
        salesByDay[i] = 0;
      }

      // For each user, get their orders
      for (final userDoc in usersSnapshot.docs) {
        final userOrdersSnapshot = await _firestore
            .collection(usersCollection)
            .doc(userDoc.id)
            .collection(ordersCollection)
            .orderBy('order_date', descending: true)
            .get();

        // Process each order
        for (final doc in userOrdersSnapshot.docs) {
          try {
            // Parse the order date
            final orderDateStr = doc.data()['order_date'] as String?;
            if (orderDateStr == null) continue;

            final orderDate = DateFormat('yyyy-MM-dd').parse(orderDateStr);

            // Calculate days difference
            final difference = now.difference(orderDate).inDays;

            // Only consider orders from the last 7 days
            if (difference >= 0 && difference < 7) {
              // Get the total amount
              final totalAmount = doc.data()['totalAmount'] ?? 0;

              // Add to the corresponding day
              salesByDay[difference] = (salesByDay[difference] ?? 0) + (totalAmount is num ? totalAmount.toDouble() : 0);
            }
          } catch (e) {
            debugPrint('Error processing order for sales data: $e');
          }
        }
      }

      // Also check the root orders collection
      final rootOrdersSnapshot = await _firestore
          .collection(ordersCollection)
          .orderBy('order_date', descending: true)
          .get();

      // Process each root order
      for (final doc in rootOrdersSnapshot.docs) {
        try {
          // Parse the order date
          final orderDateStr = doc.data()['order_date'] as String?;
          if (orderDateStr == null) continue;

          final orderDate = DateFormat('yyyy-MM-dd').parse(orderDateStr);

          // Calculate days difference
          final difference = now.difference(orderDate).inDays;

          // Only consider orders from the last 7 days
          if (difference >= 0 && difference < 7) {
            // Get the total amount
            final totalAmount = doc.data()['totalAmount'] ?? 0;

            // Add to the corresponding day
            salesByDay[difference] = (salesByDay[difference] ?? 0) + (totalAmount is num ? totalAmount.toDouble() : 0);
          }
        } catch (e) {
          debugPrint('Error processing root order for sales data: $e');
        }
      }

      // Convert to FlSpot list (reverse order to show oldest to newest)
      final List<FlSpot> spots = [];
      for (int i = 6; i >= 0; i--) {
        spots.add(FlSpot((6 - i).toDouble(), salesByDay[i] ?? 0));
      }

      return spots;
    });
  }

  // Get order status distribution
  Stream<Map<String, int>> get orderStatusDistributionStream {
    // First, get a stream of all users
    return _firestore.collection(usersCollection).snapshots().asyncMap((usersSnapshot) async {
      final Map<String, int> distribution = {
        'pending': 0,
        'processing': 0,
        'shipped': 0,
        'delivered': 0,
        'cancelled': 0,
      };

      // For each user, get their orders
      for (final userDoc in usersSnapshot.docs) {
        final userOrdersSnapshot = await _firestore
            .collection(usersCollection)
            .doc(userDoc.id)
            .collection(ordersCollection)
            .get();

        // Process each order
        for (final doc in userOrdersSnapshot.docs) {
          final status = doc.data()['status'] as String? ?? 'pending';
          distribution[status] = (distribution[status] ?? 0) + 1;
        }
      }

      // Also check the root orders collection
      final rootOrdersSnapshot = await _firestore
          .collection(ordersCollection)
          .get();

      // Process each root order
      for (final doc in rootOrdersSnapshot.docs) {
        final status = doc.data()['status'] as String? ?? 'pending';
        distribution[status] = (distribution[status] ?? 0) + 1;
      }

      return distribution;
    });
  }
}
