import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';

import '../../constants/app_constants.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import '../../widgets/search_field.dart';
import '../order/order_details_screen.dart';

class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> {
  List<OrderModel> _orders = [];
  List<OrderModel> _filteredOrders = [];
  final Map<String, Map<String, dynamic>?> _userCache = {}; // Cache user data
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedStatus = 'All';
  String _selectedDateRange = 'All';
  final List<String> _statusOptions = [
    'All',
    'Pending',
    'Processing',
    'Shipped',
    'Delivered',
    'Cancelled',
    'Returned',
  ];
  final List<String> _dateRangeOptions = [
    'All',
    'Today',
    'This Week',
    'This Month',
    'Last 30 Days',
  ];

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    try {
      final orderService = OrderService();
      final allOrders = await orderService.getAllOrders();

      // Handle empty orders list
      if (allOrders.isEmpty) {
        debugPrint('No orders found in the database');
      }

      // Pre-load user data for better performance
      await _preloadUserData(allOrders);

      if (mounted) {
        setState(() {
          _orders = allOrders;
          _applyFilters();
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading orders: $e');
      if (mounted) {
        setState(() {
          _orders = [];
          _filteredOrders = [];
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading orders: $e')),
        );
      }
    }
  }

  Future<void> _preloadUserData(List<OrderModel> orders) async {
    final orderService = OrderService();
    final uniqueUserIds = orders
        .where((order) => order.userId.isNotEmpty)
        .map((order) => order.userId)
        .toSet();

    for (final userId in uniqueUserIds) {
      if (!_userCache.containsKey(userId)) {
        try {
          final userData = await orderService.getUserInfo(userId);
          _userCache[userId] = userData;
        } catch (e) {
          debugPrint('Error loading user data for $userId: $e');
          _userCache[userId] = null;
        }
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredOrders = _orders.where((order) {
        // Apply search filter - enhanced to include customer name and amount
        final matchesSearch = _searchQuery.isEmpty ||
            order.id.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            order.userId.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            order.productId.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            order.totalAmount.toString().contains(_searchQuery) ||
            order.orderDate.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            (_userCache[order.userId]?['displayName'] ?? '')
                .toString()
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            order.items.any((item) =>
                item.productName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                item.productId.toLowerCase().contains(_searchQuery.toLowerCase()));

        // Apply status filter
        final matchesStatus = _selectedStatus == 'All' ||
            order.status.toString().split('.').last.toLowerCase() ==
                _selectedStatus.toLowerCase();

        // Apply date range filter
        final matchesDateRange = _selectedDateRange == 'All' || _matchesDateRange(order);

        return matchesSearch && matchesStatus && matchesDateRange;
      }).toList();

      // Sort by date (newest first)
      _filteredOrders.sort((a, b) {
        try {
          final dateA = DateTime.tryParse(a.orderDate) ?? DateTime.now();
          final dateB = DateTime.tryParse(b.orderDate) ?? DateTime.now();
          return dateB.compareTo(dateA);
        } catch (e) {
          return 0;
        }
      });
    });
  }

  bool _matchesDateRange(OrderModel order) {
    try {
      final orderDate = DateTime.tryParse(order.orderDate);
      if (orderDate == null) return true;

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      switch (_selectedDateRange) {
        case 'Today':
          final orderDay = DateTime(orderDate.year, orderDate.month, orderDate.day);
          return orderDay.isAtSameMomentAs(today);
        case 'This Week':
          final weekStart = today.subtract(Duration(days: today.weekday - 1));
          return orderDate.isAfter(weekStart);
        case 'This Month':
          final monthStart = DateTime(now.year, now.month, 1);
          return orderDate.isAfter(monthStart);
        case 'Last 30 Days':
          final thirtyDaysAgo = today.subtract(const Duration(days: 30));
          return orderDate.isAfter(thirtyDaysAgo);
        default:
          return true;
      }
    } catch (e) {
      return true;
    }
  }

  Future<void> _updateOrderStatus(OrderModel order, OrderStatus newStatus) async {
    try {
      // Check if userId is empty
      if (order.userId.isEmpty) {
        throw Exception('Cannot update order: User ID is empty');
      }

      final orderService = OrderService();
      await orderService.updateOrderStatus(order.userId, order.id, newStatus);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Order status updated successfully')),
        );
      }

      _loadOrders();
    } catch (e) {
      debugPrint('Error updating order status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating order status: $e')),
        );
      }
    }
  }

  void _exportOrders() {
    // TODO: Implement CSV export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality will be implemented soon'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.tryParse(dateString);
      if (date == null) return dateString;

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final orderDay = DateTime(date.year, date.month, date.day);

      if (orderDay.isAtSameMomentAs(today)) {
        return 'Today';
      } else if (orderDay.isAtSameMomentAs(today.subtract(const Duration(days: 1)))) {
        return 'Yesterday';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } catch (e) {
      return dateString;
    }
  }

  String _formatTime(String dateString) {
    try {
      final date = DateTime.tryParse(dateString);
      if (date == null) return '';

      final hour = date.hour.toString().padLeft(2, '0');
      final minute = date.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Orders',
            style: Theme.of(context).textTheme.displayMedium,
          ),
          const SizedBox(height: defaultPadding),
          // Enhanced Filters
          Column(
            children: [
              // Search and Status Row
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: SearchField(
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                          _applyFilters();
                        });
                      },
                      hintText: 'Search orders, customers, products, amounts...',
                    ),
                  ),
                  const SizedBox(width: defaultPadding),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: secondaryColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: DropdownButton<String>(
                        value: _selectedStatus,
                        dropdownColor: secondaryColor,
                        style: const TextStyle(color: Colors.white),
                        underline: Container(),
                        isExpanded: true,
                        hint: const Text('Status', style: TextStyle(color: Colors.white70)),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _selectedStatus = newValue;
                              _applyFilters();
                            });
                          }
                        },
                        items: _statusOptions
                            .map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  const SizedBox(width: defaultPadding),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: secondaryColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: DropdownButton<String>(
                        value: _selectedDateRange,
                        dropdownColor: secondaryColor,
                        style: const TextStyle(color: Colors.white),
                        underline: Container(),
                        isExpanded: true,
                        hint: const Text('Date Range', style: TextStyle(color: Colors.white70)),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _selectedDateRange = newValue;
                              _applyFilters();
                            });
                          }
                        },
                        items: _dateRangeOptions
                            .map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: defaultPadding),
              // Summary Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Showing ${_filteredOrders.length} of ${_orders.length} orders',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: _loadOrders,
                        tooltip: 'Refresh Orders',
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: _exportOrders,
                        icon: const Icon(Icons.download),
                        label: const Text('Export'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          // Orders Table
          Expanded(
            child: Card(
              color: secondaryColor,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(defaultPadding),
                child: _filteredOrders.isEmpty
                    ? const Center(
                        child: Text(
                          'No orders found',
                          style: TextStyle(color: Colors.white70),
                        ),
                      )
                    : DataTable2(
                        columnSpacing: defaultPadding,
                        minWidth: 600,
                        columns: const [
                          DataColumn(
                            label: Text('Order ID'),
                          ),
                          DataColumn(
                            label: Text('Date'),
                          ),
                          DataColumn(
                            label: Text('Customer'),
                          ),
                          DataColumn(
                            label: Text('Items'),
                          ),
                          DataColumn(
                            label: Text('Amount'),
                          ),
                          DataColumn(
                            label: Text('Status'),
                          ),
                          DataColumn(
                            label: Text('Actions'),
                          ),
                        ],
                        rows: List.generate(
                          _filteredOrders.length,
                          (index) => _buildOrderRow(_filteredOrders[index]),
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  DataRow _buildOrderRow(OrderModel order) {
    // Function to navigate to order details
    void navigateToOrderDetails() {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => OrderDetailsScreen(order: order),
        ),
      );
    }

    // Function to build customer cell using order data first, then cached data
    Widget buildCustomerCell() {
      if (order.userId.isEmpty) {
        return const Text(
          'No User ID',
          style: TextStyle(color: Colors.red),
        );
      }

      // First try to get customer info from the order data itself
      String? customerName = order.customerName;
      String? customerEmail = order.customerEmail;
      String? customerPhone = order.customerPhone;

      // If we have customer info from order data, use it
      if (customerName != null && customerName.isNotEmpty) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              customerName,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            if (customerEmail != null && customerEmail.isNotEmpty && customerEmail != customerName)
              Text(
                customerEmail,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            if (customerPhone != null && customerPhone.isNotEmpty)
              Text(
                customerPhone,
                style: const TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
          ],
        );
      }

      // Fallback to cached user data if no customer info in order
      final userData = _userCache[order.userId];
      if (userData == null) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              order.userId.length > 8 ? order.userId.substring(0, 8) : order.userId,
              style: const TextStyle(color: Colors.orange),
            ),
            const Text(
              'Loading...',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey,
              ),
            ),
          ],
        );
      }

      final userName = userData['displayName'] ?? userData['email'] ?? 'Unknown User';
      final userEmail = userData['email'] ?? '';

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            userName,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          if (userEmail.isNotEmpty && userEmail != userName)
            Text(
              userEmail,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
        ],
      );
    }

    // Function to build items cell for multi-item display
    Widget buildItemsCell() {
      if (order.items.isEmpty) {
        return const Text('No items');
      }

      if (order.items.length == 1) {
        final item = order.items.first;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              item.productName.isNotEmpty ? item.productName : item.productId,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            Text(
              'Qty: ${item.quantity}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${order.items.length} items',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            'Total Qty: ${order.quantity}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      );
    }

    return DataRow(
      cells: [
        // Order ID Cell
        DataCell(
          Text(
            order.id.length > 8 ? order.id.substring(0, 8) : order.id,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          onTap: navigateToOrderDetails,
        ),

        // Order Date Cell
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _formatDate(order.orderDate),
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                _formatTime(order.orderDate),
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          onTap: navigateToOrderDetails,
        ),

        // Customer Cell
        DataCell(
          buildCustomerCell(),
          onTap: navigateToOrderDetails,
        ),

        // Items Cell
        DataCell(
          buildItemsCell(),
          onTap: navigateToOrderDetails,
        ),

        // Amount Cell
        DataCell(
          Text('₹${order.totalAmount}'),
          onTap: navigateToOrderDetails,
        ),

        // Status Cell
        DataCell(
          _buildStatusChip(order.status),
          onTap: navigateToOrderDetails,
        ),

        // Actions Cell
        DataCell(
          Row(
            children: [
              // View Button
              IconButton(
                icon: const Icon(Icons.visibility, color: Colors.white70),
                onPressed: navigateToOrderDetails,
              ),

              // Edit Button
              IconButton(
                icon: Icon(
                  Icons.edit,
                  color: order.userId.isEmpty ? Colors.grey.withAlpha(100) : Colors.white70
                ),
                onPressed: order.userId.isEmpty
                  ? () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Cannot update order: User ID is empty')),
                      );
                    }
                  : () {
                      _showUpdateStatusDialog(order);
                    },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(OrderStatus status) {
    Color color;
    String label;

    switch (status) {
      case OrderStatus.pending:
        color = Colors.orange;
        label = 'Pending';
        break;
      case OrderStatus.processing:
        color = Colors.blue;
        label = 'Processing';
        break;
      case OrderStatus.shipped:
        color = Colors.purple;
        label = 'Shipped';
        break;
      case OrderStatus.delivered:
        color = Colors.green;
        label = 'Delivered';
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        label = 'Cancelled';
        break;
      case OrderStatus.returned:
        color = Colors.amber;
        label = 'Returned';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(51), // 0.2 opacity (51/255)
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Text(
        label,
        style: TextStyle(color: color),
      ),
    );
  }

  void _showUpdateStatusDialog(OrderModel order) {
    // Check if userId is empty
    if (order.userId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cannot update order: User ID is empty')),
      );
      return;
    }

    OrderStatus selectedStatus = order.status;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Order Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select the new status for this order:'),
            const SizedBox(height: 16),
            DropdownButton<OrderStatus>(
              value: selectedStatus,
              dropdownColor: secondaryColor,
              isExpanded: true,
              onChanged: (OrderStatus? newValue) {
                if (newValue != null) {
                  setState(() {
                    selectedStatus = newValue;
                  });
                }
              },
              items: OrderStatus.values
                  .map<DropdownMenuItem<OrderStatus>>((OrderStatus value) {
                return DropdownMenuItem<OrderStatus>(
                  value: value,
                  child: Text(
                    value.toString().split('.').last,
                    style: const TextStyle(color: Colors.white),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              if (selectedStatus != order.status) {
                _updateOrderStatus(order, selectedStatus);
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
}
