import 'package:flutter/material.dart';

import '../../constants/app_constants.dart';
import '../../services/data_sync_service.dart';
import '../../services/integration_test_service.dart';
import '../../widgets/dashboard_card.dart';

class DataSyncPage extends StatefulWidget {
  const DataSyncPage({super.key});

  @override
  State<DataSyncPage> createState() => _DataSyncPageState();
}

class _DataSyncPageState extends State<DataSyncPage> {
  final DataSyncService _dataSyncService = DataSyncService();
  final IntegrationTestService _integrationTestService = IntegrationTestService();
  bool _isLoading = false;
  Map<String, dynamic>? _lastSyncResults;
  Map<String, dynamic>? _lastVerificationResults;
  Map<String, dynamic>? _lastIntegrationResults;

  @override
  void initState() {
    super.initState();
    _runVerification();
  }

  Future<void> _runVerification() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final results = await _dataSyncService.verifyDataConsistency();
      setState(() {
        _lastVerificationResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error running verification: $e')),
        );
      }
    }
  }

  Future<void> _runSynchronization() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Data Synchronization'),
        content: const Text(
          'This will update order data to ensure compatibility between user and admin apps. '
          'This operation is safe but may take some time. Continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Continue'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // Show loading dialog
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Synchronizing data...'),
            ],
          ),
        ),
      );
    }

    try {
      final results = await _dataSyncService.synchronizeOrderData();
      
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        
        setState(() {
          _lastSyncResults = results;
        });

        // Show results dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Synchronization Complete'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Orders Processed: ${results['orders_processed']}'),
                  Text('Orders Updated: ${results['orders_updated']}'),
                  if (results['field_fixes'] != null && (results['field_fixes'] as Map).isNotEmpty) ...[
                    const SizedBox(height: 8),
                    const Text('Field Fixes:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...((results['field_fixes'] as Map).entries.map((entry) => 
                        Text('• ${entry.key}: ${entry.value}'))),
                  ],
                  if (results['errors'] != null && (results['errors'] as List).isNotEmpty) ...[
                    const SizedBox(height: 8),
                    const Text('Errors:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...((results['errors'] as List).take(5).map((error) => Text('• $error'))),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _runVerification(); // Refresh verification
                },
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error during synchronization: $e')),
        );
      }
    }
  }

  Future<void> _createAdminCopies() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Admin Order Copies'),
        content: const Text(
          'This will create admin-friendly copies of all user orders in a separate collection. '
          'This helps improve admin app performance. Continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Continue'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // Show loading dialog
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Creating admin copies...'),
            ],
          ),
        ),
      );
    }

    try {
      final results = await _dataSyncService.createAdminOrderCopies();
      
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        
        // Show results dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Admin Copies Created'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Orders Processed: ${results['orders_processed']}'),
                Text('Admin Orders Created: ${results['admin_orders_created']}'),
                Text('Admin Orders Updated: ${results['admin_orders_updated']}'),
                if (results['errors'] != null && (results['errors'] as List).isNotEmpty) ...[
                  const SizedBox(height: 8),
                  const Text('Errors:', style: TextStyle(fontWeight: FontWeight.bold)),
                  ...((results['errors'] as List).take(5).map((error) => Text('• $error'))),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating admin copies: $e')),
        );
      }
    }
  }

  Future<void> _runIntegrationTests() async {
    // Show loading dialog
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Running integration tests...'),
            ],
          ),
        ),
      );
    }

    try {
      final results = await _integrationTestService.runIntegrationTests();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        setState(() {
          _lastIntegrationResults = results;
        });

        // Show results dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Integration Test Results'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Overall Status: ${results['overall_status']?.toString().toUpperCase()}'),
                  if (results['summary'] != null) ...[
                    const SizedBox(height: 8),
                    Text('Tests Passed: ${results['summary']['passed']}'),
                    Text('Tests Failed: ${results['summary']['failed']}'),
                    Text('Warnings: ${results['summary']['warnings']}'),
                    Text('Success Rate: ${results['summary']['success_rate']}%'),
                  ],
                  if (results['recommendations'] != null && (results['recommendations'] as List).isNotEmpty) ...[
                    const SizedBox(height: 8),
                    const Text('Recommendations:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...((results['recommendations'] as List).take(5).map((rec) => Text('• $rec'))),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error running integration tests: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Data Synchronization',
                            style: Theme.of(context).textTheme.displayMedium,
                          ),
                          Text(
                            'Cross-App Data Consistency Management',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: _runIntegrationTests,
                            icon: const Icon(Icons.integration_instructions),
                            label: const Text('Run Tests'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purple,
                              foregroundColor: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed: _runVerification,
                            icon: const Icon(Icons.refresh),
                            label: const Text('Refresh'),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: defaultPadding),

                  // Verification Results
                  if (_lastVerificationResults != null) ...[
                    Row(
                      children: [
                        Expanded(
                          child: DashboardCard(
                            title: 'Total Orders',
                            value: _lastVerificationResults!['total_orders']?.toString() ?? '0',
                            icon: Icons.shopping_cart,
                            color: primaryColor,
                          ),
                        ),
                        const SizedBox(width: defaultPadding),
                        Expanded(
                          child: DashboardCard(
                            title: 'Parseable Orders',
                            value: _lastVerificationResults!['parseable_orders']?.toString() ?? '0',
                            icon: Icons.check_circle,
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(width: defaultPadding),
                        Expanded(
                          child: DashboardCard(
                            title: 'Unparseable Orders',
                            value: _lastVerificationResults!['unparseable_orders']?.toString() ?? '0',
                            icon: Icons.error,
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(width: defaultPadding),
                        Expanded(
                          child: DashboardCard(
                            title: 'Compatibility Rate',
                            value: _lastVerificationResults!['total_orders'] != null && 
                                   _lastVerificationResults!['total_orders'] > 0
                                ? '${((_lastVerificationResults!['parseable_orders'] ?? 0) / _lastVerificationResults!['total_orders'] * 100).toStringAsFixed(1)}%'
                                : '0%',
                            icon: Icons.analytics,
                            color: Colors.purple,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: defaultPadding * 2),
                  ],

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: Card(
                          color: secondaryColor,
                          child: Padding(
                            padding: const EdgeInsets.all(defaultPadding),
                            child: Column(
                              children: [
                                const Icon(Icons.sync, size: 48, color: Colors.blue),
                                const SizedBox(height: 8),
                                const Text(
                                  'Synchronize Data',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'Fix field inconsistencies and ensure compatibility between user and admin apps',
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _runSynchronization,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('Run Synchronization'),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: defaultPadding),
                      Expanded(
                        child: Card(
                          color: secondaryColor,
                          child: Padding(
                            padding: const EdgeInsets.all(defaultPadding),
                            child: Column(
                              children: [
                                const Icon(Icons.copy, size: 48, color: Colors.green),
                                const SizedBox(height: 8),
                                const Text(
                                  'Create Admin Copies',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'Create admin-friendly copies of user orders for better performance',
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _createAdminCopies,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('Create Copies'),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: defaultPadding * 2),

                  // Results Display
                  if (_lastVerificationResults != null) ...[
                    Card(
                      color: secondaryColor,
                      child: Padding(
                        padding: const EdgeInsets.all(defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Verification Results',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: defaultPadding),
                            
                            // Missing Fields
                            if (_lastVerificationResults!['missing_fields'] != null &&
                                (_lastVerificationResults!['missing_fields'] as Map).isNotEmpty) ...[
                              const Text(
                                'Missing Fields:',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              ...(_lastVerificationResults!['missing_fields'] as Map).entries.map(
                                (entry) => Padding(
                                  padding: const EdgeInsets.only(left: 16, bottom: 4),
                                  child: Text('• ${entry.key}: ${entry.value} orders'),
                                ),
                              ),
                              const SizedBox(height: 16),
                            ],

                            // Recommendations
                            if (_lastVerificationResults!['recommendations'] != null &&
                                (_lastVerificationResults!['recommendations'] as List).isNotEmpty) ...[
                              const Text(
                                'Recommendations:',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              ...(_lastVerificationResults!['recommendations'] as List).map(
                                (recommendation) => Padding(
                                  padding: const EdgeInsets.only(left: 16, bottom: 4),
                                  child: Text('• $recommendation'),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],

                  // Last Sync Results
                  if (_lastSyncResults != null) ...[
                    const SizedBox(height: defaultPadding),
                    Card(
                      color: secondaryColor,
                      child: Padding(
                        padding: const EdgeInsets.all(defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Last Synchronization Results',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: defaultPadding),
                            Text('Completed: ${_lastSyncResults!['timestamp']}'),
                            Text('Orders Processed: ${_lastSyncResults!['orders_processed']}'),
                            Text('Orders Updated: ${_lastSyncResults!['orders_updated']}'),
                            
                            if (_lastSyncResults!['field_fixes'] != null &&
                                (_lastSyncResults!['field_fixes'] as Map).isNotEmpty) ...[
                              const SizedBox(height: 8),
                              const Text(
                                'Field Fixes Applied:',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              ...(_lastSyncResults!['field_fixes'] as Map).entries.take(10).map(
                                (entry) => Padding(
                                  padding: const EdgeInsets.only(left: 16, bottom: 2),
                                  child: Text('• ${entry.key}: ${entry.value}'),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}
