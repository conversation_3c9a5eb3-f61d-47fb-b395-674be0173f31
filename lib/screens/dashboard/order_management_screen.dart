import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../../constants/app_constants.dart';
import '../../models/order_model.dart';
import '../../services/order_service.dart';
import '../order/order_details_screen.dart';

class OrderManagementScreen extends StatefulWidget {
  const OrderManagementScreen({super.key});

  @override
  State<OrderManagementScreen> createState() => _OrderManagementScreenState();
}

class _OrderManagementScreenState extends State<OrderManagementScreen> {
  final OrderService _orderService = OrderService();
  List<OrderModel> _allOrders = [];
  List<OrderModel> _ordersWithIssues = [];
  bool _isLoading = true;
  bool _isFixing = false;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final orders = await _orderService.getAllOrders();

      // Handle empty orders list
      if (orders.isEmpty) {
        debugPrint('No orders found in the database');
      }

      if (mounted) {
        setState(() {
          _allOrders = orders;
          _ordersWithIssues = orders.where((order) => order.userId.isEmpty).toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading orders: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading orders: $e')),
        );
        setState(() {
          _allOrders = [];
          _ordersWithIssues = [];
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showUserSelectionDialog({OrderModel? singleOrder}) async {
    // Get all users
    final users = await _orderService.getAllUsers();

    if (users.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No users found to assign orders to')),
        );
      }
      return;
    }

    // Show dialog to select user
    if (mounted) {
      String? selectedUserId = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(singleOrder != null
              ? 'Select User for Order #${singleOrder.id.substring(0, 8)}'
              : 'Select User for All Orders'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: users.length,
              itemBuilder: (context, index) {
                final user = users[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: primaryColor.withAlpha(51),
                    child: Text(
                      ((user['displayName'] as String).isNotEmpty
                        ? (user['displayName'] as String)[0]
                        : '?').toUpperCase(),
                      style: const TextStyle(
                        color: primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(user['displayName'] as String),
                  subtitle: Text(user['email'] as String),
                  onTap: () {
                    Navigator.of(context).pop(user['id'] as String);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );

      if (selectedUserId != null) {
        if (singleOrder != null) {
          await _fixSingleOrder(singleOrder, selectedUserId);
        } else {
          await _fixAllOrders(selectedUserId);
        }
      }
    }
  }

  Future<void> _fixAllOrders(String userId) async {
    if (_ordersWithIssues.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No orders need fixing')),
      );
      return;
    }

    setState(() {
      _isFixing = true;
    });

    EasyLoading.show(status: 'Fixing orders...');

    try {
      final fixedCount = await _orderService.fixAllOrdersWithMissingUserIds(userId);

      EasyLoading.dismiss();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Fixed $fixedCount orders')),
        );

        // Reload orders to see the changes
        await _loadOrders();
      }
    } catch (e) {
      EasyLoading.dismiss();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fixing orders: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isFixing = false;
        });
      }
    }
  }

  Future<void> _fixSingleOrder(OrderModel order, String userId) async {
    EasyLoading.show(status: 'Fixing order...');

    try {
      final success = await _orderService.fixOrderWithMissingUserId(order.id, userId);

      EasyLoading.dismiss();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                ? 'Order fixed successfully'
                : 'Failed to fix order'
            ),
          ),
        );

        // Reload orders to see the changes
        if (success) {
          await _loadOrders();
        }
      }
    } catch (e) {
      EasyLoading.dismiss();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fixing order: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Management'),
        actions: [
          TextButton.icon(
            onPressed: _loadOrders,
            icon: const Icon(Icons.refresh, color: Colors.white),
            label: const Text(
              'Refresh',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Stats Card
                  Card(
                    color: secondaryColor,
                    elevation: 0,
                    child: Padding(
                      padding: const EdgeInsets.all(defaultPadding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Order Statistics',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: defaultPadding),
                          Row(
                            children: [
                              Expanded(
                                child: _buildStatCard(
                                  'Total Orders',
                                  _allOrders.length.toString(),
                                  Icons.shopping_cart,
                                  Colors.blue,
                                ),
                              ),
                              const SizedBox(width: defaultPadding),
                              Expanded(
                                child: _buildStatCard(
                                  'Orders with Issues',
                                  _ordersWithIssues.length.toString(),
                                  Icons.error_outline,
                                  Colors.orange,
                                ),
                              ),
                              const SizedBox(width: defaultPadding),
                              Expanded(
                                child: _buildStatCard(
                                  'Valid Orders',
                                  (_allOrders.length - _ordersWithIssues.length).toString(),
                                  Icons.check_circle_outline,
                                  Colors.green,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: defaultPadding),

                  // Orders with Issues Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Orders with Missing User IDs',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      ElevatedButton.icon(
                        onPressed: _isFixing ? null : () => _showUserSelectionDialog(),
                        icon: const Icon(Icons.build),
                        label: const Text('Fix All Orders'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: defaultPadding),

                  // Orders List
                  Expanded(
                    child: _ordersWithIssues.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: 64,
                                ),
                                const SizedBox(height: defaultPadding),
                                Text(
                                  'No orders with issues found',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _ordersWithIssues.length,
                            itemBuilder: (context, index) {
                              final order = _ordersWithIssues[index];
                              return _buildOrderCard(order);
                            },
                          ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(100)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    return Card(
      color: secondaryColor,
      elevation: 0,
      margin: const EdgeInsets.only(bottom: defaultPadding / 2),
      child: ListTile(
        contentPadding: const EdgeInsets.all(defaultPadding / 2),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.orange.withAlpha(30),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange),
          ),
          child: const Icon(
            Icons.error_outline,
            color: Colors.orange,
          ),
        ),
        title: Text(
          'Order #${order.id.substring(0, 8)}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Date: ${order.orderDate}'),
            Text('Amount: ₹${order.totalAmount}'),
            Text('Status: ${order.status.toString().split('.').last}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.build, color: Colors.orange),
              tooltip: 'Fix Order',
              onPressed: () => _showUserSelectionDialog(singleOrder: order),
            ),
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.white70),
              tooltip: 'View Order',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => OrderDetailsScreen(order: order),
                  ),
                );
              },
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OrderDetailsScreen(order: order),
            ),
          );
        },
      ),
    );
  }
}
