import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../constants/app_constants.dart';
import '../../services/payment_service.dart';
import '../../widgets/dashboard_card.dart';

class PaymentsPage extends StatefulWidget {
  const PaymentsPage({super.key});

  @override
  State<PaymentsPage> createState() => _PaymentsPageState();
}

class _PaymentsPageState extends State<PaymentsPage> {
  final PaymentService _paymentService = PaymentService();
  bool _isLoading = true;
  Map<String, dynamic> _statistics = {};
  List<Map<String, dynamic>> _payments = [];
  String _searchQuery = '';
  String _selectedStatus = 'All';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final statistics = await _paymentService.getPaymentStatistics();
      final payments = await _paymentService.getAllPayments();

      setState(() {
        _statistics = statistics;
        _payments = payments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading payment data: $e')),
        );
      }
    }
  }

  Future<void> _bulkVerifyPayments() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Verifying payments...'),
          ],
        ),
      ),
    );

    try {
      final result = await _paymentService.bulkVerifyPayments();
      
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Bulk Verification Complete'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Total Processed: ${result['total_processed']}'),
                Text('Successful: ${result['success_count']}'),
                Text('Failed: ${result['failure_count']}'),
                if (result['errors'] != null && (result['errors'] as List).isNotEmpty) ...[
                  const SizedBox(height: 8),
                  const Text('Errors:', style: TextStyle(fontWeight: FontWeight.bold)),
                  ...((result['errors'] as List).take(5).map((error) => Text('• $error'))),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _loadData(); // Refresh data
                },
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error during bulk verification: $e')),
        );
      }
    }
  }

  List<Map<String, dynamic>> get _filteredPayments {
    return _payments.where((payment) {
      final matchesSearch = _searchQuery.isEmpty ||
          payment['orderId']?.toString().toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
          payment['paymentId']?.toString().toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
          payment['customerName']?.toString().toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
          payment['customerEmail']?.toString().toLowerCase().contains(_searchQuery.toLowerCase()) == true;

      final matchesStatus = _selectedStatus == 'All' ||
          payment['status']?.toString().toLowerCase() == _selectedStatus.toLowerCase();

      return matchesSearch && matchesStatus;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Payment Management',
                            style: Theme.of(context).textTheme.displayMedium,
                          ),
                          Text(
                            'Razorpay Integration & Verification',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: _bulkVerifyPayments,
                            icon: const Icon(Icons.verified),
                            label: const Text('Bulk Verify'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed: _loadData,
                            icon: const Icon(Icons.refresh),
                            label: const Text('Refresh'),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: defaultPadding),

                  // Statistics Cards
                  Row(
                    children: [
                      Expanded(
                        child: DashboardCard(
                          title: 'Total Payments',
                          value: _statistics['total_payments']?.toString() ?? '0',
                          icon: Icons.payment,
                          color: primaryColor,
                        ),
                      ),
                      const SizedBox(width: defaultPadding),
                      Expanded(
                        child: DashboardCard(
                          title: 'Verified',
                          value: _statistics['verified_payments']?.toString() ?? '0',
                          icon: Icons.verified,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(width: defaultPadding),
                      Expanded(
                        child: DashboardCard(
                          title: 'Unverified',
                          value: _statistics['unverified_payments']?.toString() ?? '0',
                          icon: Icons.warning,
                          color: Colors.orange,
                        ),
                      ),
                      const SizedBox(width: defaultPadding),
                      Expanded(
                        child: DashboardCard(
                          title: 'Total Amount',
                          value: '₹${NumberFormat('#,##,###').format(_statistics['total_amount'] ?? 0)}',
                          icon: Icons.account_balance_wallet,
                          color: Colors.purple,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: defaultPadding),

                  // Verification Rate Card
                  Row(
                    children: [
                      Expanded(
                        child: DashboardCard(
                          title: 'Verification Rate',
                          value: '${(_statistics['verification_rate'] ?? 0).toStringAsFixed(1)}%',
                          icon: Icons.analytics,
                          color: Colors.teal,
                        ),
                      ),
                      const SizedBox(width: defaultPadding),
                      Expanded(
                        child: DashboardCard(
                          title: 'Verified Amount',
                          value: '₹${NumberFormat('#,##,###').format(_statistics['verified_amount'] ?? 0)}',
                          icon: Icons.check_circle,
                          color: Colors.green.shade600,
                        ),
                      ),
                      const SizedBox(width: defaultPadding * 2),
                      const SizedBox(width: defaultPadding * 2),
                    ],
                  ),
                  const SizedBox(height: defaultPadding * 2),

                  // Search and Filter
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextField(
                          decoration: const InputDecoration(
                            hintText: 'Search by Order ID, Payment ID, Customer...',
                            prefixIcon: Icon(Icons.search),
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _searchQuery = value;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: defaultPadding),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedStatus,
                          decoration: const InputDecoration(
                            labelText: 'Status',
                            border: OutlineInputBorder(),
                          ),
                          items: ['All', 'pending', 'processing', 'shipped', 'delivered', 'cancelled']
                              .map((status) => DropdownMenuItem(
                                    value: status,
                                    child: Text(status),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedStatus = value ?? 'All';
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: defaultPadding),

                  // Payments Table
                  Card(
                    color: secondaryColor,
                    elevation: 0,
                    child: Padding(
                      padding: const EdgeInsets.all(defaultPadding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Payment Records (${_filteredPayments.length})',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: defaultPadding),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('Order ID')),
                                DataColumn(label: Text('Payment ID')),
                                DataColumn(label: Text('Customer')),
                                DataColumn(label: Text('Amount')),
                                DataColumn(label: Text('Status')),
                                DataColumn(label: Text('Verified')),
                                DataColumn(label: Text('Date')),
                              ],
                              rows: _filteredPayments.map((payment) {
                                return DataRow(
                                  cells: [
                                    DataCell(Text(payment['orderId']?.toString() ?? '')),
                                    DataCell(Text(payment['paymentId']?.toString() ?? '')),
                                    DataCell(Text(payment['customerName']?.toString() ?? 'Unknown')),
                                    DataCell(Text('₹${NumberFormat('#,###').format(payment['amount'] ?? 0)}')),
                                    DataCell(
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: _getStatusColor(payment['status']?.toString() ?? ''),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          payment['status']?.toString().toUpperCase() ?? 'UNKNOWN',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                    DataCell(
                                      Icon(
                                        payment['isVerified'] == true ? Icons.check_circle : Icons.warning,
                                        color: payment['isVerified'] == true ? Colors.green : Colors.orange,
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        payment['orderDate'] != null
                                            ? DateFormat('MMM dd, yyyy').format(
                                                (payment['orderDate'] as Timestamp).toDate(),
                                              )
                                            : 'Unknown',
                                      ),
                                    ),
                                  ],
                                );
                              }).toList(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'shipped':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
