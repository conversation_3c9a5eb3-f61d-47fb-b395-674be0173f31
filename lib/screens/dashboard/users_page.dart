import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';

import '../../constants/app_constants.dart';
import '../../models/user.dart';
import '../../widgets/search_field.dart';
import '../user/user_details_screen.dart';
import '../user/user_management_screen.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<AppUser> _users = [];
  List<AppUser> _filteredUsers = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      // Use a cache-busting query to ensure we get fresh data
      final snapshot = await _firestore
          .collection(usersCollection)
          .get();

      // Check if there are any documents
      if (snapshot.docs.isEmpty) {
        debugPrint('No users found in the database');
        if (mounted) {
          setState(() {
            _users = [];
            _filteredUsers = [];
            _isLoading = false;
          });
        }
        return;
      }

      final users = snapshot.docs
          .map((doc) {
            try {
              return AppUser.fromMap(doc.data(), doc.id);
            } catch (e) {
              debugPrint('Error parsing user document ${doc.id}: $e');
              // Return a placeholder user with minimal data if parsing fails
              return AppUser(
                id: doc.id,
                email: doc.data()['email'] ?? 'No Email',
                favouriteProducts: [],
                createdAt: DateTime.now(),
              );
            }
          })
          .toList();

      debugPrint('Loaded ${users.length} users from Firestore');

      if (mounted) {
        setState(() {
          _users = users;
          _applyFilters();
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading users: $e');
      if (mounted) {
        setState(() {
          _users = [];
          _filteredUsers = [];
          _isLoading = false;
        });
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredUsers = _users.where((user) {
        // Apply search filter
        final matchesSearch = _searchQuery.isEmpty ||
            (user.displayName?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
            user.email.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            (user.phoneNumber?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

        return matchesSearch;
      }).toList();
    });
  }

  Future<void> _refreshUserList() async {
    setState(() {
      _isLoading = true;
    });

    await _loadUsers();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User list refreshed')),
      );
    }
  }

  Future<void> _toggleUserStatus(AppUser user) async {
    try {
      await _firestore.collection(usersCollection).doc(user.id).update({
        'isActive': !user.isActive,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('User ${user.isActive ? 'deactivated' : 'activated'} successfully')),
        );
      }

      _loadUsers();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating user status: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Users',
                style: Theme.of(context).textTheme.displayMedium,
              ),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: _refreshUserList,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Refresh'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: defaultPadding),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const UserManagementScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.manage_accounts),
                    label: const Text('User Management'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          // Search
          SearchField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
                _applyFilters();
              });
            },
            hintText: 'Search users...',
          ),
          const SizedBox(height: defaultPadding),
          // Users Table
          Expanded(
            child: Card(
              color: secondaryColor,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(defaultPadding),
                child: _filteredUsers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.person_off,
                              size: 64,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: defaultPadding),
                            const Text(
                              'No users found',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(height: defaultPadding),
                            ElevatedButton.icon(
                              onPressed: _refreshUserList,
                              icon: const Icon(Icons.refresh),
                              label: const Text('Refresh'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: primaryColor,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      )
                    : DataTable2(
                        columnSpacing: defaultPadding,
                        minWidth: 600,
                        columns: const [
                          DataColumn(
                            label: Text('User'),
                          ),
                          DataColumn(
                            label: Text('Email'),
                          ),
                          DataColumn(
                            label: Text('Phone'),
                          ),
                          DataColumn(
                            label: Text('Status'),
                          ),
                          DataColumn(
                            label: Text('Actions'),
                          ),
                        ],
                        rows: List.generate(
                          _filteredUsers.length,
                          (index) => _buildUserRow(_filteredUsers[index]),
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  DataRow _buildUserRow(AppUser user) {
    return DataRow(
      cells: [
        DataCell(
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: primaryColor.withAlpha(51), // 0.2 opacity (51/255)
                backgroundImage: user.displayPicture != null
                    ? NetworkImage(user.displayPicture!)
                    : null,
                child: user.displayPicture == null
                    ? Text(
                        (user.displayName?.isNotEmpty ?? false)
                            ? user.displayName![0].toUpperCase()
                            : (user.email.isNotEmpty
                                ? user.email[0].toUpperCase()
                                : '?'),
                        style: const TextStyle(
                          color: primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: defaultPadding / 2),
              Text(
                user.displayName ?? 'No Name',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => UserDetailsScreen(user: user),
              ),
            );
          },
        ),
        DataCell(
          Text(user.email),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => UserDetailsScreen(user: user),
              ),
            );
          },
        ),
        DataCell(
          Text(user.phoneNumber ?? 'Not provided'),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => UserDetailsScreen(user: user),
              ),
            );
          },
        ),
        DataCell(
          _buildStatusChip(user.isActive),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => UserDetailsScreen(user: user),
              ),
            );
          },
        ),
        DataCell(
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.visibility, color: Colors.white70),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => UserDetailsScreen(user: user),
                    ),
                  );
                },
              ),
              IconButton(
                icon: Icon(
                  user.isActive ? Icons.block : Icons.check_circle,
                  color: user.isActive ? Colors.red : Colors.green,
                ),
                onPressed: () {
                  _showToggleStatusDialog(user);
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isActive ? Colors.green.withAlpha(51) : Colors.red.withAlpha(51), // 0.2 opacity (51/255)
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: isActive ? Colors.green : Colors.red),
      ),
      child: Text(
        isActive ? 'Active' : 'Inactive',
        style: TextStyle(color: isActive ? Colors.green : Colors.red),
      ),
    );
  }

  void _showToggleStatusDialog(AppUser user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${user.isActive ? 'Deactivate' : 'Activate'} User'),
        content: Text(
          'Are you sure you want to ${user.isActive ? 'deactivate' : 'activate'} this user?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _toggleUserStatus(user);
            },
            child: Text(
              user.isActive ? 'Deactivate' : 'Activate',
              style: TextStyle(
                color: user.isActive ? Colors.red : Colors.green,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
