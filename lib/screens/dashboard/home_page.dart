import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../constants/app_constants.dart';
import '../../services/dashboard_service.dart';
import '../../widgets/chart_container.dart';
import '../../widgets/dashboard_card.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final DashboardService _dashboardService = DashboardService();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Dashboard',
            style: Theme.of(context).textTheme.displayMedium,
          ),
          Text(
            'Welcome to $appName',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: defaultPadding),
          // Stats Cards
          Row(
            children: [
              // Total Products Card
              Expanded(
                child: StreamBuilder<int>(
                  stream: _dashboardService.totalProductsStream,
                  builder: (context, snapshot) {
                    return DashboardCard(
                      title: 'Total Products',
                      value: snapshot.hasData ? snapshot.data.toString() : '...',
                      icon: Icons.shopping_bag,
                      color: primaryColor,
                    );
                  },
                ),
              ),
              const SizedBox(width: defaultPadding),
              // Total Users Card
              Expanded(
                child: StreamBuilder<int>(
                  stream: _dashboardService.totalUsersStream,
                  builder: (context, snapshot) {
                    return DashboardCard(
                      title: 'Total Users',
                      value: snapshot.hasData ? snapshot.data.toString() : '...',
                      icon: Icons.people,
                      color: Colors.orange,
                    );
                  },
                ),
              ),
              const SizedBox(width: defaultPadding),
              // Total Orders Card
              Expanded(
                child: StreamBuilder<int>(
                  stream: _dashboardService.totalOrdersStream,
                  builder: (context, snapshot) {
                    return DashboardCard(
                      title: 'Total Orders',
                      value: snapshot.hasData ? snapshot.data.toString() : '...',
                      icon: Icons.shopping_cart,
                      color: Colors.green,
                    );
                  },
                ),
              ),
              const SizedBox(width: defaultPadding),
              // Pending Orders Card
              Expanded(
                child: StreamBuilder<int>(
                  stream: _dashboardService.pendingOrdersStream,
                  builder: (context, snapshot) {
                    return DashboardCard(
                      title: 'Pending Orders',
                      value: snapshot.hasData ? snapshot.data.toString() : '...',
                      icon: Icons.pending_actions,
                      color: Colors.red,
                    );
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          // Revenue Cards
          Row(
            children: [
              // Total Revenue Card
              Expanded(
                child: FutureBuilder<double>(
                  future: _dashboardService.getTotalRevenue(),
                  builder: (context, snapshot) {
                    final revenue = snapshot.data ?? 0.0;
                    return DashboardCard(
                      title: 'Total Revenue',
                      value: '₹${NumberFormat('#,##,###').format(revenue)}',
                      icon: Icons.account_balance_wallet,
                      color: Colors.purple,
                    );
                  },
                ),
              ),
              const SizedBox(width: defaultPadding),
              // Monthly Revenue Card
              Expanded(
                child: FutureBuilder<double>(
                  future: _dashboardService.getMonthlyRevenue(),
                  builder: (context, snapshot) {
                    final revenue = snapshot.data ?? 0.0;
                    return DashboardCard(
                      title: 'This Month',
                      value: '₹${NumberFormat('#,##,###').format(revenue)}',
                      icon: Icons.trending_up,
                      color: Colors.teal,
                    );
                  },
                ),
              ),
              const SizedBox(width: defaultPadding),
              // Average Order Value
              Expanded(
                child: StreamBuilder<int>(
                  stream: _dashboardService.totalOrdersStream,
                  builder: (context, orderSnapshot) {
                    return FutureBuilder<double>(
                      future: _dashboardService.getTotalRevenue(),
                      builder: (context, revenueSnapshot) {
                        final orders = orderSnapshot.data ?? 0;
                        final revenue = revenueSnapshot.data ?? 0.0;
                        final avgValue = orders > 0 ? revenue / orders : 0.0;
                        return DashboardCard(
                          title: 'Avg Order Value',
                          value: '₹${NumberFormat('#,###').format(avgValue)}',
                          icon: Icons.analytics,
                          color: Colors.indigo,
                        );
                      },
                    );
                  },
                ),
              ),
              const SizedBox(width: defaultPadding),
              // Growth Rate (placeholder)
              Expanded(
                child: DashboardCard(
                  title: 'Growth Rate',
                  value: '+12.5%',
                  icon: Icons.show_chart,
                  color: Colors.green.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),
          // Charts
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sales Chart
              Expanded(
                flex: 2,
                child: StreamBuilder<List<FlSpot>>(
                  stream: _dashboardService.weeklySalesDataStream,
                  builder: (context, snapshot) {
                    final salesData = snapshot.data ?? [];
                    return ChartContainer(
                      title: 'Weekly Sales',
                      chart: LineChart(
                        LineChartData(
                          gridData: const FlGridData(show: false),
                          titlesData: FlTitlesData(
                            leftTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                            bottomTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: (value, meta) {
                                  final now = DateTime.now();
                                  final day = now.subtract(
                                      Duration(days: (6 - value).toInt()));
                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      DateFormat('E').format(day),
                                      style: const TextStyle(
                                        color: Colors.white70,
                                        fontSize: 12,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            rightTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                            topTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                          ),
                          borderData: FlBorderData(show: false),
                          lineBarsData: [
                            LineChartBarData(
                              spots: salesData,
                              isCurved: true,
                              color: primaryColor,
                              barWidth: 4,
                              isStrokeCapRound: true,
                              dotData: const FlDotData(show: false),
                              belowBarData: BarAreaData(
                                show: true,
                                color: primaryColor.withAlpha(51), // 0.2 opacity (51/255)
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: defaultPadding),
              // Recent Orders
              Expanded(
                child: StreamBuilder<List<Map<String, dynamic>>>(
                  stream: _dashboardService.recentOrdersStream,
                  builder: (context, snapshot) {
                    final recentOrders = snapshot.data ?? [];
                    return ChartContainer(
                      title: 'Recent Orders',
                      child: recentOrders.isEmpty
                          ? const Center(
                              child: Text(
                                'No recent orders',
                                style: TextStyle(color: Colors.white70),
                              ),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              itemCount: recentOrders.length,
                              itemBuilder: (context, index) {
                                final order = recentOrders[index];
                                return ListTile(
                                  title: Text(
                                    'Order #${order['id'].substring(0, min(order['id'].length, 8))}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  subtitle: Text(
                                    'Date: ${order['date']}',
                                    style: const TextStyle(color: Colors.white70),
                                  ),
                                  trailing: _buildStatusChip(order['status']),
                                );
                              },
                            ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'pending':
        color = Colors.orange;
        break;
      case 'processing':
        color = Colors.blue;
        break;
      case 'shipped':
        color = Colors.purple;
        break;
      case 'delivered':
        color = Colors.green;
        break;
      case 'cancelled':
        color = Colors.red;
        break;
      case 'returned':
        color = Colors.amber;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        status,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      ),
      backgroundColor: color,
      padding: EdgeInsets.zero,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
}
