import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';

import '../../constants/app_constants.dart';
import '../../models/order_model.dart';
import '../../models/user.dart';
import '../../services/order_service.dart';
import '../order/order_details_screen.dart';

class UserDetailsScreen extends StatefulWidget {
  final AppUser user;

  const UserDetailsScreen({super.key, required this.user});

  @override
  State<UserDetailsScreen> createState() => _UserDetailsScreenState();
}

class _UserDetailsScreenState extends State<UserDetailsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final OrderService _orderService = OrderService();

  bool _isLoading = false;
  List<OrderModel> _userOrders = [];
  Map<String, dynamic> _userStats = {
    'totalOrders': 0,
    'totalSpent': 0.0,
    'lastOrderDate': null,
    'favoriteProducts': [],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load user orders
      final orders = await _orderService.getUserOrders(widget.user.id);

      // Calculate user stats
      double totalSpent = 0;
      DateTime? lastOrderDate;

      for (final order in orders) {
        totalSpent += order.totalAmount;

        if (lastOrderDate == null || _parseOrderDate(order.orderDate).isAfter(lastOrderDate)) {
          lastOrderDate = _parseOrderDate(order.orderDate);
        }
      }

      setState(() {
        _userOrders = orders;
        _userStats = {
          'totalOrders': orders.length,
          'totalSpent': totalSpent,
          'lastOrderDate': lastOrderDate,
          'favoriteProducts': widget.user.favouriteProducts,
        };
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading user data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading user data: $e')),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  DateTime _parseOrderDate(String dateStr) {
    try {
      // Try different date formats
      final formats = [
        'yyyy-MM-dd',
        'dd-MM-yyyy',
        'MM/dd/yyyy',
        'd-M-yyyy',
      ];

      for (final format in formats) {
        try {
          return DateFormat(format).parse(dateStr);
        } catch (_) {
          // Try next format
        }
      }

      // If all formats fail, return current date
      return DateTime.now();
    } catch (e) {
      debugPrint('Error parsing date: $e');
      return DateTime.now();
    }
  }

  Future<void> _toggleUserStatus() async {
    try {
      EasyLoading.show(status: 'Updating user status...');

      await _firestore.collection(usersCollection).doc(widget.user.id).update({
        'isActive': !widget.user.isActive,
      });

      EasyLoading.dismiss();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('User ${widget.user.isActive ? 'deactivated' : 'activated'} successfully')),
        );

        // Update local state
        setState(() {
          widget.user.isActive = !widget.user.isActive;
        });
      }
    } catch (e) {
      EasyLoading.dismiss();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating user status: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('User: ${widget.user.displayName ?? 'No Name'}'),
        actions: [
          TextButton.icon(
            onPressed: _toggleUserStatus,
            icon: Icon(
              widget.user.isActive ? Icons.block : Icons.check_circle,
              color: widget.user.isActive ? Colors.red : Colors.green,
            ),
            label: Text(
              widget.user.isActive ? 'Deactivate' : 'Activate',
              style: TextStyle(
                color: widget.user.isActive ? Colors.red : Colors.green,
              ),
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Profile'),
            Tab(text: 'Orders'),
            Tab(text: 'Stats'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildProfileTab(),
                _buildOrdersTab(),
                _buildStatsTab(),
              ],
            ),
    );
  }

  Widget _buildProfileTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Profile Card
          Card(
            color: secondaryColor,
            elevation: 0,
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: primaryColor.withAlpha(51),
                    backgroundImage: widget.user.displayPicture != null
                        ? NetworkImage(widget.user.displayPicture!)
                        : null,
                    child: widget.user.displayPicture == null
                        ? Text(
                            (widget.user.displayName?.isNotEmpty ?? false)
                                ? widget.user.displayName![0].toUpperCase()
                                : (widget.user.email.isNotEmpty
                                    ? widget.user.email[0].toUpperCase()
                                    : '?'),
                            style: const TextStyle(
                              color: primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 36,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: defaultPadding),

                  // User Name
                  Text(
                    widget.user.displayName ?? 'No Name',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),

                  // Status Badge
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: defaultPadding / 2),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: widget.user.isActive
                          ? Colors.green.withAlpha(51)
                          : Colors.red.withAlpha(51),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: widget.user.isActive ? Colors.green : Colors.red,
                      ),
                    ),
                    child: Text(
                      widget.user.isActive ? 'Active' : 'Inactive',
                      style: TextStyle(
                        color: widget.user.isActive ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: defaultPadding),

          // User Details
          Card(
            color: secondaryColor,
            elevation: 0,
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'User Details',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const Divider(),
                  _buildDetailItem(
                    icon: Icons.email,
                    title: 'Email',
                    value: widget.user.email,
                  ),
                  _buildDetailItem(
                    icon: Icons.phone,
                    title: 'Phone',
                    value: widget.user.phoneNumber ?? 'Not provided',
                  ),
                  _buildDetailItem(
                    icon: Icons.calendar_today,
                    title: 'Joined',
                    value: DateFormat('MMM dd, yyyy').format(widget.user.createdAt),
                  ),
                  _buildDetailItem(
                    icon: Icons.access_time,
                    title: 'Last Login',
                    value: widget.user.lastLogin != null
                        ? DateFormat('MMM dd, yyyy').format(widget.user.lastLogin!)
                        : 'Never',
                  ),
                  _buildDetailItem(
                    icon: Icons.shopping_cart,
                    title: 'Total Orders',
                    value: _userStats['totalOrders'].toString(),
                  ),
                  _buildDetailItem(
                    icon: Icons.attach_money,
                    title: 'Total Spent',
                    value: '₹${_userStats['totalSpent'].toStringAsFixed(2)}',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: defaultPadding),

          // Favorite Products
          Card(
            color: secondaryColor,
            elevation: 0,
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Favorite Products',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const Divider(),
                  widget.user.favouriteProducts.isEmpty
                      ? const Padding(
                          padding: EdgeInsets.all(defaultPadding),
                          child: Center(
                            child: Text('No favorite products'),
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: widget.user.favouriteProducts.length,
                          itemBuilder: (context, index) {
                            final productId = widget.user.favouriteProducts[index];
                            return FutureBuilder<DocumentSnapshot>(
                              future: _firestore.collection(productsCollection).doc(productId).get(),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState == ConnectionState.waiting) {
                                  return const ListTile(
                                    title: Text('Loading...'),
                                  );
                                }

                                if (snapshot.hasError || !snapshot.hasData || !snapshot.data!.exists) {
                                  return ListTile(
                                    title: Text('Product ID: $productId'),
                                    subtitle: const Text('Product not found'),
                                  );
                                }

                                final productData = snapshot.data!.data() as Map<String, dynamic>;
                                return ListTile(
                                  leading: productData['images'] != null &&
                                          (productData['images'] as List).isNotEmpty
                                      ? ClipRRect(
                                          borderRadius: BorderRadius.circular(8),
                                          child: Image.network(
                                            productData['images'][0],
                                            width: 50,
                                            height: 50,
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error, stackTrace) {
                                              return Container(
                                                width: 50,
                                                height: 50,
                                                color: Colors.grey.withAlpha(51),
                                                child: const Icon(Icons.image_not_supported),
                                              );
                                            },
                                          ),
                                        )
                                      : Container(
                                          width: 50,
                                          height: 50,
                                          color: Colors.grey.withAlpha(51),
                                          child: const Icon(Icons.image_not_supported),
                                        ),
                                  title: Text(productData['title'] ?? 'No Title'),
                                  subtitle: Text('₹${productData['price'] ?? 'N/A'}'),
                                );
                              },
                            );
                          },
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersTab() {
    return _userOrders.isEmpty
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.shopping_cart_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: defaultPadding),
                Text(
                  'No orders found',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(defaultPadding),
            itemCount: _userOrders.length,
            itemBuilder: (context, index) {
              final order = _userOrders[index];
              return Card(
                color: secondaryColor,
                elevation: 0,
                margin: const EdgeInsets.only(bottom: defaultPadding),
                child: ListTile(
                  contentPadding: const EdgeInsets.all(defaultPadding / 2),
                  leading: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _getStatusColor(order.status).withAlpha(51),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: _getStatusColor(order.status)),
                    ),
                    child: Icon(
                      Icons.shopping_cart,
                      color: _getStatusColor(order.status),
                    ),
                  ),
                  title: Text(
                    'Order #${order.id.substring(0, 8)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Date: ${order.orderDate}'),
                      Text('Amount: ₹${order.totalAmount}'),
                      Text('Status: ${order.status.toString().split('.').last}'),
                    ],
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.visibility, color: Colors.white70),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => OrderDetailsScreen(order: order),
                        ),
                      );
                    },
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => OrderDetailsScreen(order: order),
                      ),
                    );
                  },
                ),
              );
            },
          );
  }

  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stats Overview
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'Total Orders',
                  value: _userStats['totalOrders'].toString(),
                  icon: Icons.shopping_cart,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: defaultPadding),
              Expanded(
                child: _buildStatCard(
                  title: 'Total Spent',
                  value: '₹${_userStats['totalSpent'].toStringAsFixed(2)}',
                  icon: Icons.attach_money,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          // Last Order
          Card(
            color: secondaryColor,
            elevation: 0,
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Last Order',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const Divider(),
                  _userStats['lastOrderDate'] != null
                      ? Padding(
                          padding: const EdgeInsets.symmetric(vertical: defaultPadding / 2),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today, color: Colors.white70),
                              const SizedBox(width: defaultPadding / 2),
                              Text(
                                DateFormat('MMM dd, yyyy').format(_userStats['lastOrderDate']),
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        )
                      : const Padding(
                          padding: EdgeInsets.all(defaultPadding),
                          child: Center(
                            child: Text('No orders yet'),
                          ),
                        ),
                ],
              ),
            ),
          ),
          const SizedBox(height: defaultPadding),

          // Order Status Distribution
          Card(
            color: secondaryColor,
            elevation: 0,
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order Status Distribution',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const Divider(),
                  _userOrders.isEmpty
                      ? const Padding(
                          padding: EdgeInsets.all(defaultPadding),
                          child: Center(
                            child: Text('No orders to analyze'),
                          ),
                        )
                      : Column(
                          children: _buildOrderStatusDistribution(),
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: defaultPadding / 2),
      child: Row(
        children: [
          Icon(icon, color: Colors.white70),
          const SizedBox(width: defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: secondaryColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withAlpha(51)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding / 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildOrderStatusDistribution() {
    // Count orders by status
    final Map<OrderStatus, int> statusCounts = {};
    for (final order in _userOrders) {
      statusCounts[order.status] = (statusCounts[order.status] ?? 0) + 1;
    }

    // Create status bars
    return OrderStatus.values.map((status) {
      final count = statusCounts[status] ?? 0;
      final percentage = _userOrders.isEmpty ? 0 : (count / _userOrders.length * 100).toInt();

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: defaultPadding / 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  status.toString().split('.').last,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text('$count orders ($percentage%)'),
              ],
            ),
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.grey.withAlpha(51),
              valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor(status)),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      );
    }).toList();
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.processing:
        return Colors.blue;
      case OrderStatus.shipped:
        return Colors.purple;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
      case OrderStatus.returned:
        return Colors.amber;
    }
  }
}
