import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../constants/app_constants.dart';
import '../../services/data_migration_service.dart';

class DataMigrationScreen extends StatefulWidget {
  const DataMigrationScreen({super.key});

  @override
  State<DataMigrationScreen> createState() => _DataMigrationScreenState();
}

class _DataMigrationScreenState extends State<DataMigrationScreen> {
  final DataMigrationService _migrationService = DataMigrationService();
  bool _isMigrating = false;
  MigrationResult? _lastResult;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Migration'),
        backgroundColor: primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Card(
              color: secondaryColor,
              child: Padding(
                padding: const EdgeInsets.all(defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.sync,
                          color: primaryColor,
                          size: 32,
                        ),
                        const SizedBox(width: defaultPadding),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Order Data Migration',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Standardize order data between Admin and User app formats',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: defaultPadding * 2),

            // Migration Info
            const Card(
              child: Padding(
                padding: EdgeInsets.all(defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'What this migration does:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: defaultPadding),
                    Text('• Standardizes field names (userId/user_id, orderDate/order_date, etc.)'),
                    Text('• Ensures compatibility between Admin and User apps'),
                    Text('• Fixes orphaned orders without valid user IDs'),
                    Text('• Adds both field name formats for maximum compatibility'),
                    Text('• Preserves all existing data'),
                    SizedBox(height: defaultPadding),
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'This operation is safe and will not delete any data. It only adds missing field formats.',
                            style: TextStyle(
                              fontStyle: FontStyle.italic,
                              color: Colors.orange,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: defaultPadding * 2),

            // Migration Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isMigrating ? null : _startMigration,
                icon: _isMigrating
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.play_arrow),
                label: Text(_isMigrating ? 'Migrating...' : 'Start Migration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ),

            const SizedBox(height: defaultPadding * 2),

            // Results
            if (_lastResult != null) ...[
              Card(
                color: _lastResult!.isSuccessful ? successColor : errorColor,
                child: Padding(
                  padding: const EdgeInsets.all(defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _lastResult!.isSuccessful ? Icons.check_circle : Icons.error,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _lastResult!.isSuccessful ? 'Migration Completed' : 'Migration Completed with Errors',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: defaultPadding),
                      Text(
                        'Total Processed: ${_lastResult!.totalProcessed}',
                        style: const TextStyle(color: Colors.white),
                      ),
                      Text(
                        'Successful: ${_lastResult!.successfulMigrations}',
                        style: const TextStyle(color: Colors.white),
                      ),
                      Text(
                        'Errors: ${_lastResult!.errors}',
                        style: const TextStyle(color: Colors.white),
                      ),
                      Text(
                        'Success Rate: ${(_lastResult!.successRate * 100).toStringAsFixed(1)}%',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ),

              // Error Details
              if (_lastResult!.errorMessages.isNotEmpty) ...[
                const SizedBox(height: defaultPadding),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Error Details:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 200,
                          child: ListView.builder(
                            itemCount: _lastResult!.errorMessages.length,
                            itemBuilder: (context, index) {
                              return Padding(
                                padding: const EdgeInsets.symmetric(vertical: 2),
                                child: Text(
                                  '• ${_lastResult!.errorMessages[index]}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.red,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _startMigration() async {
    setState(() {
      _isMigrating = true;
      _lastResult = null;
    });

    try {
      EasyLoading.show(status: 'Starting migration...');

      final result = await _migrationService.migrateAllOrders();

      setState(() {
        _lastResult = result;
        _isMigrating = false;
      });

      EasyLoading.dismiss();

      if (result.isSuccessful) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Migration completed successfully! Processed ${result.totalProcessed} orders.'),
            backgroundColor: successColor,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Migration completed with ${result.errors} errors. Check details below.'),
            backgroundColor: warningColor,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isMigrating = false;
        _lastResult = MigrationResult(
          totalProcessed: 0,
          successfulMigrations: 0,
          errors: 1,
          errorMessages: ['Migration failed: $e'],
        );
      });

      EasyLoading.dismiss();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Migration failed: $e'),
          backgroundColor: errorColor,
        ),
      );
    }
  }
}
