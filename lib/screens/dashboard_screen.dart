import 'package:flutter/material.dart';
import 'package:flutter_admin_scaffold/admin_scaffold.dart';

import '../constants/app_constants.dart';
import '../services/auth_service.dart';
import 'activity/activity_log_screen.dart';
import 'brand/brand_management_screen.dart';
import 'carousel/carousel_management_screen.dart' if (dart.library.html) 'carousel/carousel_management_screen_web.dart';
import 'category/category_management_screen.dart';
import 'coupon/coupon_management_screen.dart';
import 'dashboard/data_sync_page.dart';
import 'dashboard/home_page.dart';
import 'dashboard/order_management_screen.dart';
import 'dashboard/orders_page.dart';
import 'dashboard/payments_page.dart';
import 'dashboard/products_page.dart';
import 'dashboard/settings_page.dart';
import 'dashboard/users_page.dart';
import 'login_screen.dart';
import 'migration/data_migration_screen.dart';
import 'reports/reports_screen.dart';
import 'user/user_management_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  Widget _selectedScreen = const HomePage();
  String _selectedRoute = '/';
  AdminRole _userRole = AdminRole.viewer;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserRole();
  }

  Future<void> _loadUserRole() async {
    try {
      final role = await AuthService().getAdminRole();
      setState(() {
        _userRole = role;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading user role: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _handleScreenChanged(AdminMenuItem item) {
    setState(() {
      _selectedRoute = item.route!;
      switch (item.route) {
        case '/':
          _selectedScreen = const HomePage();
          break;
        case '/products':
          _selectedScreen = const ProductsPage();
          break;
        case '/orders':
          _selectedScreen = const OrdersPage();
          break;
        case '/payments':
          _selectedScreen = const PaymentsPage();
          break;
        case '/order-management':
          _selectedScreen = const OrderManagementScreen();
          break;
        case '/users':
          _selectedScreen = const UsersPage();
          break;
        case '/user-management':
          _selectedScreen = const UserManagementScreen();
          break;
        case '/carousel':
          _selectedScreen = const CarouselManagementScreen();
          break;
        case '/categories':
          _selectedScreen = const CategoryManagementScreen();
          break;
        case '/brands':
          _selectedScreen = const BrandManagementScreen();
          break;
        case '/coupons':
          _selectedScreen = const CouponManagementScreen();
          break;
        case '/reports':
          _selectedScreen = const ReportsScreen();
          break;
        case '/activity-logs':
          _selectedScreen = const ActivityLogScreen();
          break;
        case '/migration':
          _selectedScreen = const DataMigrationScreen();
          break;
        case '/data-sync':
          _selectedScreen = const DataSyncPage();
          break;
        case '/settings':
          _selectedScreen = const SettingsPage();
          break;
        default:
          _selectedScreen = const HomePage();
      }
    });
  }

  Future<void> _signOut() async {
    try {
      await AuthService().signOut();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error signing out: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return AdminScaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        title: const Text(appName),
        backgroundColor: secondaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _signOut,
            tooltip: 'Logout',
          ),
        ],
      ),
      sideBar: SideBar(
        backgroundColor: secondaryColor,
        activeBackgroundColor: primaryColor,
        activeIconColor: Colors.white,
        activeTextStyle: const TextStyle(color: Colors.white),
        textStyle: const TextStyle(color: Colors.white70),
        items: _buildSideBarItems(),
        selectedRoute: _selectedRoute,
        onSelected: _handleScreenChanged,
        header: Container(
          height: 50,
          width: double.infinity,
          color: secondaryColor,
          child: const Center(
            child: Text(
              'Admin Panel',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        footer: Container(
          height: 50,
          width: double.infinity,
          color: secondaryColor,
          child: Center(
            child: Text(
              'Role: ${_userRole.toString().split('.').last}',
              style: const TextStyle(
                color: Colors.white70,
              ),
            ),
          ),
        ),
      ),
      body: _selectedScreen,
    );
  }

  List<AdminMenuItem> _buildSideBarItems() {
    final items = <AdminMenuItem>[
      const AdminMenuItem(
        title: 'Dashboard',
        route: '/',
        icon: Icons.dashboard,
      ),
      const AdminMenuItem(
        title: 'Products',
        route: '/products',
        icon: Icons.shopping_bag,
      ),
      const AdminMenuItem(
        title: 'Orders',
        route: '/orders',
        icon: Icons.shopping_cart,
      ),
      const AdminMenuItem(
        title: 'Payments',
        route: '/payments',
        icon: Icons.payment,
      ),
    ];

    // Add Order Management for admin and superAdmin
    if (_userRole == AdminRole.admin || _userRole == AdminRole.superAdmin) {
      items.add(const AdminMenuItem(
        title: 'Order Management',
        route: '/order-management',
        icon: Icons.build,
      ));
    }

    // Only show users management for admin and superAdmin
    if (_userRole == AdminRole.admin || _userRole == AdminRole.superAdmin) {
      items.add(const AdminMenuItem(
        title: 'Users',
        route: '/users',
        icon: Icons.people,
      ));

      items.add(const AdminMenuItem(
        title: 'User Management',
        route: '/user-management',
        icon: Icons.manage_accounts,
      ));

      // Add Carousel Management
      items.add(const AdminMenuItem(
        title: 'Carousel',
        route: '/carousel',
        icon: Icons.slideshow,
      ));
      
      // Add Category Management
      items.add(const AdminMenuItem(
        title: 'Categories',
        route: '/categories',
        icon: Icons.category,
      ));
      
      // Add Brand Management
      items.add(const AdminMenuItem(
        title: 'Brands',
        route: '/brands',
        icon: Icons.business,
      ));
      
      // Add Coupon Management
      items.add(const AdminMenuItem(
        title: 'Coupons',
        route: '/coupons',
        icon: Icons.local_offer,
      ));
      
      // Add Reports
      items.add(const AdminMenuItem(
        title: 'Reports',
        route: '/reports',
        icon: Icons.bar_chart,
      ));
      
      // Add Activity Logs
      items.add(const AdminMenuItem(
        title: 'Activity Logs',
        route: '/activity-logs',
        icon: Icons.history,
      ));
    }

    // Add Data Migration for super admins only
    if (_userRole == AdminRole.superAdmin) {
      items.add(const AdminMenuItem(
        title: 'Data Migration',
        route: '/migration',
        icon: Icons.sync,
      ));

      // Add Data Sync for super admins
      items.add(const AdminMenuItem(
        title: 'Data Sync',
        route: '/data-sync',
        icon: Icons.sync_alt,
      ));
    }

    items.add(const AdminMenuItem(
      title: 'Settings',
      route: '/settings',
      icon: Icons.settings,
    ));

    return items;
  }
}
