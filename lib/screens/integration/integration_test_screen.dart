import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../../constants/app_constants.dart';
import '../../services/integration_test_service.dart';

class IntegrationTestScreen extends StatefulWidget {
  const IntegrationTestScreen({super.key});

  @override
  State<IntegrationTestScreen> createState() => _IntegrationTestScreenState();
}

class _IntegrationTestScreenState extends State<IntegrationTestScreen> {
  final IntegrationTestService _testService = IntegrationTestService();
  Map<String, dynamic>? _testResults;
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cross-App Integration Tests'),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Container(
        color: bgColor,
        child: Column(
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(defaultPadding),
              decoration: BoxDecoration(
                color: secondaryColor,
                border: Border(
                  bottom: BorderSide(color: Colors.grey[700]!),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.integration_instructions,
                        color: primaryColor,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Order Management Integration Tests',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Verify that order data flows correctly between the user app and admin app',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

            // Test Controls
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(defaultPadding),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isRunning ? null : _runTests,
                      icon: _isRunning
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(Icons.play_arrow),
                      label: Text(_isRunning ? 'Running Tests...' : 'Run Integration Tests'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _testResults != null ? _clearResults : null,
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear Results'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[700],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ],
              ),
            ),

            // Test Results
            Expanded(
              child: _testResults != null
                  ? _buildTestResults()
                  : _buildEmptyState(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.science,
            size: 64,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'No test results yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[400],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Click "Run Integration Tests" to start',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResults() {
    final tests = _testResults!['tests'] as Map<String, dynamic>;
    final summary = _testResults!['summary'] as Map<String, dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Card
          _buildSummaryCard(summary),
          const SizedBox(height: 16),

          // Individual Test Results
          ...tests.entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildTestCard(entry.key, entry.value as Map<String, dynamic>),
              )),

          // Recommendations
          if (_testResults!['recommendations'] != null) ...[
            const SizedBox(height: 16),
            _buildRecommendationsCard(_testResults!['recommendations'] as List<String>),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard(Map<String, dynamic> summary) {
    final overallStatus = summary['overall_status'] as String;
    final totalTests = summary['total_tests'] as int;
    final passedTests = summary['passed_tests'] as int;
    final failedTests = summary['failed_tests'] as int;
    final warningTests = summary['warning_tests'] as int;

    Color statusColor;
    IconData statusIcon;
    switch (overallStatus) {
      case 'passed':
        statusColor = successColor;
        statusIcon = Icons.check_circle;
        break;
      case 'warning':
        statusColor = warningColor;
        statusIcon = Icons.warning;
        break;
      case 'failed':
        statusColor = errorColor;
        statusIcon = Icons.error;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Card(
      color: secondaryColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Test Summary',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    overallStatus.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatItem('Total', totalTests.toString(), Colors.blue),
                _buildStatItem('Passed', passedTests.toString(), successColor),
                _buildStatItem('Warnings', warningTests.toString(), warningColor),
                _buildStatItem('Failed', failedTests.toString(), errorColor),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Expanded(
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestCard(String testName, Map<String, dynamic> testResult) {
    final status = testResult['status'] as String;
    final issues = testResult['issues'] as List<dynamic>? ?? [];

    Color statusColor;
    IconData statusIcon;
    switch (status) {
      case 'passed':
        statusColor = successColor;
        statusIcon = Icons.check_circle;
        break;
      case 'warning':
        statusColor = warningColor;
        statusIcon = Icons.warning;
        break;
      case 'failed':
        statusColor = errorColor;
        statusIcon = Icons.error;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Card(
      color: secondaryColor,
      child: ExpansionTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          _getTestDisplayName(testName),
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          '${issues.length} issues found',
          style: TextStyle(color: Colors.grey[400]),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Test details
                ...testResult.entries
                    .where((e) => e.key != 'status' && e.key != 'issues')
                    .map((e) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            children: [
                              Text(
                                '${e.key}: ',
                                style: TextStyle(color: Colors.grey[400]),
                              ),
                              Text(
                                e.value.toString(),
                                style: const TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        )),

                // Issues
                if (issues.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Text(
                    'Issues:',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...issues.map((issue) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.error_outline, color: errorColor, size: 16),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                issue.toString(),
                                style: TextStyle(color: Colors.grey[300]),
                              ),
                            ),
                          ],
                        ),
                      )),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsCard(List<String> recommendations) {
    if (recommendations.isEmpty) return const SizedBox.shrink();

    return Card(
      color: secondaryColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: warningColor, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Recommendations',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...recommendations.map((rec) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.arrow_right, color: warningColor, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          rec,
                          style: TextStyle(color: Colors.grey[300]),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  String _getTestDisplayName(String testName) {
    switch (testName) {
      case 'id_consistency':
        return 'ID Consistency';
      case 'data_structure':
        return 'Data Structure Compatibility';
      case 'order_parsing':
        return 'Order Parsing & Display';
      case 'address_integration':
        return 'Address Integration';
      case 'status_updates':
        return 'Status Update Synchronization';
      case 'user_data':
        return 'User Data Consistency';
      case 'payment_integration':
        return 'Payment Integration';
      case 'collection_structure':
        return 'Collection Structure';
      case 'order_lifecycle':
        return 'Order Lifecycle';
      default:
        return testName.replaceAll('_', ' ').toUpperCase();
    }
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = null;
    });

    try {
      EasyLoading.show(status: 'Running integration tests...');
      final results = await _testService.runIntegrationTests();
      
      setState(() {
        _testResults = results;
      });

      EasyLoading.showSuccess('Tests completed');
    } catch (e) {
      EasyLoading.showError('Test failed: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
      EasyLoading.dismiss();
    }
  }

  void _clearResults() {
    setState(() {
      _testResults = null;
    });
  }
}
