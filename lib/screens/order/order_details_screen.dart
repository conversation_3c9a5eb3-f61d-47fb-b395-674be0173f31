import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:logger/logger.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../constants/app_constants.dart';
import '../../models/address_model.dart';
import '../../models/order_model.dart';
import '../../models/product.dart';
import '../../services/address_service.dart';
import '../../services/customer_service.dart';
import '../../services/order_service.dart';
import '../../services/payment_service.dart';
import '../../widgets/address_map_view.dart';

class OrderDetailsScreen extends StatefulWidget {
  final OrderModel order;

  const OrderDetailsScreen({super.key, required this.order});

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final OrderService _orderService = OrderService();
  final CustomerService _customerService = CustomerService();
  final AddressService _addressService = AddressService();
  final PaymentService _paymentService = PaymentService();
  final Logger _logger = Logger();

  late OrderModel _order;
  bool _isLoading = true;
  List<Product> _products = []; // Support multiple products
  Map<String, dynamic>? _userData;
  AddressModel? _addressModel;
  final _trackingNumberController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _order = widget.order;
    _trackingNumberController.text = _order.trackingNumber ?? '';
    _loadData();
  }

  @override
  void dispose() {
    _trackingNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load product data for all items
      final List<Product> loadedProducts = [];
      for (final item in _order.items) {
        if (item.productId.isNotEmpty) {
          try {
            final productDoc = await _firestore.collection(productsCollection).doc(item.productId).get();
            if (productDoc.exists) {
              loadedProducts.add(Product.fromMap(productDoc.data()!, productDoc.id));
            }
          } catch (e) {
            _logger.w('Error loading product ${item.productId}: $e');
          }
        }
      }

      setState(() {
        _products = loadedProducts;
      });

      if (_order.items.isEmpty) {
        _logger.w('No items in order, skipping product data loading');
      }

      // Load user data using the CustomerService
      if (_order.userId.isNotEmpty) {
        try {
          // First try to get the customer using the CustomerService
          final customer = await _customerService.getCustomerById(_order.userId);
          if (customer != null) {
            setState(() {
              _userData = {
                'id': customer.id,
                'email': customer.email,
                'displayName': customer.displayName ?? 'No Name',
                'phone': customer.phoneNumber ?? 'No Phone',
              };
            });
          } else {
            // Fall back to the OrderService if CustomerService fails
            final userData = await _orderService.getUserInfo(_order.userId);
            if (userData != null) {
              setState(() {
                _userData = userData;
              });
            }
          }
        } catch (e) {
          _logger.e('Error loading customer data: $e');
          // Fall back to the OrderService if CustomerService fails
          final userData = await _orderService.getUserInfo(_order.userId);
          if (userData != null) {
            setState(() {
              _userData = userData;
            });
          }
        }
      } else {
        _logger.w('User ID is empty, skipping user data loading');
      }

      // Parse shipping address if available
      if (_order.shippingAddress != null && _order.shippingAddress!.isNotEmpty) {
        try {
          // Try to find an existing address model in the database
          final addresses = await _addressService.getUserAddresses(_order.userId);
          for (final address in addresses) {
            if (address.formattedAddress.contains(_order.shippingAddress!)) {
              setState(() {
                _addressModel = address;
              });
              break;
            }
          }

          // If no matching address found, try to parse the address string
          if (_addressModel == null && _order.userId.isNotEmpty) {
            _addressModel = AddressModel.parseFromString(_order.shippingAddress!, _order.userId);
          }
        } catch (e) {
          _logger.e('Error parsing shipping address: $e');
        }
      }
    } catch (e) {
      _logger.e('Error loading data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateOrderStatus(OrderStatus newStatus) async {
    try {
      // Check if userId is empty
      if (_order.userId.isEmpty) {
        throw Exception('Cannot update order: User ID is empty');
      }

      EasyLoading.show(status: 'Updating order status...');
      await _orderService.updateOrderStatus(_order.userId, _order.id, newStatus);

      // Update local order object
      setState(() {
        _order = _order.copyWith(status: newStatus);
      });

      EasyLoading.dismiss();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Order status updated successfully')),
        );
      }
    } catch (e) {
      EasyLoading.dismiss();
      _logger.e('Error updating order status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating order status: $e')),
        );
      }
    }
  }

  Future<void> _updateTrackingNumber() async {
    final trackingNumber = _trackingNumberController.text.trim();

    try {
      // Check if userId is empty
      if (_order.userId.isEmpty) {
        throw Exception('Cannot update order: User ID is empty');
      }

      EasyLoading.show(status: 'Updating tracking number...');
      await _orderService.updateOrderTracking(_order.userId, _order.id, trackingNumber);

      // Update local order object
      setState(() {
        _order = _order.copyWith(trackingNumber: trackingNumber);
      });

      EasyLoading.dismiss();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tracking number updated successfully')),
        );
        Navigator.pop(context); // Close the dialog
      }
    } catch (e) {
      EasyLoading.dismiss();
      _logger.e('Error updating tracking number: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating tracking number: $e')),
        );
      }
    }
  }

  Future<void> _updatePaymentStatus(bool isPaid) async {
    try {
      // Check if userId is empty
      if (_order.userId.isEmpty) {
        throw Exception('Cannot update order: User ID is empty');
      }

      EasyLoading.show(status: 'Updating payment status...');
      await _orderService.updateOrderPaymentStatus(_order.userId, _order.id, isPaid);

      // Update local order object
      setState(() {
        _order = _order.copyWith(isPaid: isPaid);
      });

      EasyLoading.dismiss();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Payment status updated successfully')),
        );
      }
    } catch (e) {
      EasyLoading.dismiss();
      _logger.e('Error updating payment status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating payment status: $e')),
        );
      }
    }
  }

  void _showUpdateStatusDialog() {
    OrderStatus selectedStatus = _order.status;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Order Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select the new status for this order:'),
            const SizedBox(height: 16),
            DropdownButton<OrderStatus>(
              value: selectedStatus,
              isExpanded: true,
              onChanged: (OrderStatus? newValue) {
                if (newValue != null) {
                  setState(() {
                    selectedStatus = newValue;
                  });
                }
              },
              items: OrderStatus.values
                  .map<DropdownMenuItem<OrderStatus>>((OrderStatus value) {
                return DropdownMenuItem<OrderStatus>(
                  value: value,
                  child: Text(value.toString().split('.').last),
                );
              }).toList(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              if (selectedStatus != _order.status) {
                _updateOrderStatus(selectedStatus);
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showUpdateTrackingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Tracking Number'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Enter the tracking number for this order:'),
            const SizedBox(height: 16),
            TextField(
              controller: _trackingNumberController,
              decoration: const InputDecoration(
                labelText: 'Tracking Number',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: _updateTrackingNumber,
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAssignCustomerDialog() async {
    final TextEditingController customerIdController = TextEditingController();

    try {
      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Assign to Customer'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Enter the customer ID to assign this order to. This will move the order to the customer\'s orders collection.',
              ),
              const SizedBox(height: defaultPadding),
              TextField(
                controller: customerIdController,
                decoration: const InputDecoration(
                  labelText: 'Customer ID',
                  hintText: 'Enter Firebase User ID',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final customerId = customerIdController.text.trim();
                if (customerId.isNotEmpty) {
                  Navigator.pop(context, customerId);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please enter a customer ID')),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Assign'),
            ),
          ],
        ),
      );

      if (result != null && result.isNotEmpty) {
        await _assignOrderToCustomer(result);
      }
    } finally {
      customerIdController.dispose();
    }
  }

  Future<void> _assignOrderToCustomer(String customerId) async {
    try {
      EasyLoading.show(status: 'Assigning order to customer...');

      final success = await _customerService.fixOrderWithMissingCustomerId(_order.id, customerId);

      EasyLoading.dismiss();

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Order successfully assigned to customer'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate back to the previous screen
          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to assign order to customer'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      EasyLoading.dismiss();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error assigning order to customer: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Order Details'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Order #${_order.id.substring(0, 8)}'),
        actions: [
          if (_order.userId.isNotEmpty)
            TextButton.icon(
              onPressed: _showUpdateStatusDialog,
              icon: const Icon(Icons.edit, color: Colors.white),
              label: const Text(
                'Update Status',
                style: TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Warning banner for orders with empty user ID
            if (_order.userId.isEmpty)
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(bottom: defaultPadding),
                padding: const EdgeInsets.all(defaultPadding),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(40),
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.warning_amber_rounded, color: Colors.red),
                        const SizedBox(width: 8),
                        const Text(
                          'Limited Functionality Order',
                          style: TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This order has no associated user ID. You can view the order details, but you cannot update the order status, tracking information, or payment status.',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            // Order Status Card
            Card(
              color: secondaryColor,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Order Status',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        _buildStatusChip(_order.status),
                      ],
                    ),
                    const SizedBox(height: defaultPadding),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Order Date',
                                style: TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              Text(_order.orderDate),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Payment Status',
                                style: TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              Row(
                                children: [
                                  Text(_order.isPaid ? 'Paid' : 'Unpaid'),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    icon: Icon(
                                      _order.isPaid ? Icons.check_circle : Icons.cancel,
                                      color: _order.isPaid ? Colors.green : Colors.red,
                                      size: 20,
                                    ),
                                    onPressed: _order.userId.isEmpty
                                      ? () {
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            const SnackBar(content: Text('Cannot update payment status: User ID is empty')),
                                          );
                                        }
                                      : () => _updatePaymentStatus(!_order.isPaid),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    if (_order.status == OrderStatus.shipped || _order.status == OrderStatus.delivered) ...[
                      const Divider(color: Colors.white24),
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Tracking Number',
                                  style: TextStyle(
                                    color: Colors.white70,
                                  ),
                                ),
                                Row(
                                  children: [
                                    Text(_order.trackingNumber ?? 'Not available'),
                                    const SizedBox(width: 8),
                                    IconButton(
                                      icon: const Icon(
                                        Icons.edit,
                                        color: Colors.white70,
                                        size: 20,
                                      ),
                                      onPressed: _order.userId.isEmpty
                                        ? () {
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              const SnackBar(content: Text('Cannot update tracking: User ID is empty')),
                                            );
                                          }
                                        : _showUpdateTrackingDialog,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          if (_order.shippedDate != null)
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Shipped Date',
                                    style: TextStyle(
                                      color: Colors.white70,
                                    ),
                                  ),
                                  Text(
                                    '${_order.shippedDate!.day}/${_order.shippedDate!.month}/${_order.shippedDate!.year}',
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ],
                    if (_order.status == OrderStatus.delivered && _order.deliveredDate != null) ...[
                      const Divider(color: Colors.white24),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Delivered Date',
                            style: TextStyle(
                              color: Colors.white70,
                            ),
                          ),
                          Text(
                            '${_order.deliveredDate!.day}/${_order.deliveredDate!.month}/${_order.deliveredDate!.year}',
                          ),
                        ],
                      ),
                    ],
                    if (_order.status == OrderStatus.cancelled && _order.cancelReason != null) ...[
                      const Divider(color: Colors.white24),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Cancellation Reason',
                            style: TextStyle(
                              color: Colors.white70,
                            ),
                          ),
                          Text(_order.cancelReason!),
                        ],
                      ),
                    ],
                    if (_order.status == OrderStatus.returned && _order.returnReason != null) ...[
                      const Divider(color: Colors.white24),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Return Reason',
                            style: TextStyle(
                              color: Colors.white70,
                            ),
                          ),
                          Text(_order.returnReason!),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: defaultPadding),

            // Customer Information
            Card(
              color: secondaryColor,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Customer Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: defaultPadding),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Customer ID',
                                style: TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              Text(_order.userId),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Customer Name',
                                style: TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              Text(_order.customerName ?? _userData?['displayName'] ?? 'Unknown'),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: defaultPadding),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Email',
                                style: TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              Text(_order.customerEmail ?? _userData?['email'] ?? 'Unknown'),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Phone',
                                style: TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              Text(_order.customerPhone ?? _userData?['phone'] ?? 'Unknown'),
                            ],
                          ),
                        ),
                      ],
                    ),
                    if (_order.shippingAddress != null) ...[
                      const SizedBox(height: defaultPadding),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.local_shipping,
                                color: primaryColor,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Shipping Address',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[850],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey[700]!),
                            ),
                            child: Text(
                              _order.shippingAddress!,
                              style: const TextStyle(
                                fontSize: 14,
                                height: 1.5,
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),
                          // Debug information about the address
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.black54,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.grey.shade700),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Address Debug Info:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.amber,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text('User ID: ${_order.userId}'),
                                Text('Address Model: ${_addressModel != null ? 'Found' : 'Not Found'}'),
                                if (_addressModel != null) ...[
                                  Text('Coordinates: ${_addressModel!.latitude}, ${_addressModel!.longitude}'),
                                  Text('Formatted: ${_addressModel!.formattedAddress}'),
                                ],
                                const SizedBox(height: 4),
                                ElevatedButton(
                                  onPressed: () => _parseAndSaveAddress(_order.shippingAddress!),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: primaryColor,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('Parse & Save Address'),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12),
                          // Map view of the shipping address
                          AddressMapView(
                            address: _order.shippingAddress!,
                            addressModel: _addressModel,
                            height: 200,
                            showInfoWindow: true,
                          ),
                          const SizedBox(height: 8),
                          // Link to open in maps app
                          InkWell(
                            onTap: () {
                              _openMapWithAddress(_order.shippingAddress!);
                            },
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.directions,
                                  color: primaryColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Get Directions',
                                  style: TextStyle(
                                    color: primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],

                    // Add button to fix customer ID issue
                    if (_order.userId.isEmpty) ...[
                      const SizedBox(height: defaultPadding),
                      const Divider(color: Colors.white24),
                      const SizedBox(height: defaultPadding),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _showAssignCustomerDialog,
                              icon: const Icon(Icons.person_add),
                              label: const Text('Assign to Customer'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: defaultPadding),

            // Product Information
            Card(
              color: secondaryColor,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _order.items.length > 1 ? 'Products Information (${_order.items.length} items)' : 'Product Information',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: defaultPadding),
                    if (_order.items.isNotEmpty) ...[
                      // Display all items
                      ...List.generate(_order.items.length, (index) {
                        final item = _order.items[index];
                        final product = _products.length > index ? _products[index] : null;

                        return Container(
                          margin: const EdgeInsets.only(bottom: defaultPadding),
                          padding: const EdgeInsets.all(defaultPadding),
                          decoration: BoxDecoration(
                            color: Colors.grey[850],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Product Image
                              Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: Colors.grey[800],
                                  borderRadius: BorderRadius.circular(8),
                                  image: product?.images.isNotEmpty == true
                                      ? DecorationImage(
                                          image: NetworkImage(product!.images.first),
                                          fit: BoxFit.cover,
                                        )
                                      : null,
                                ),
                                child: product?.images.isEmpty != false
                                    ? const Center(
                                        child: Icon(
                                          Icons.image_not_supported,
                                          color: Colors.white54,
                                          size: 32,
                                        ),
                                      )
                                    : null,
                              ),
                              const SizedBox(width: defaultPadding),
                              // Product Details
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product?.title ?? item.productName,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    if (product?.variant.isNotEmpty == true)
                                      Text(
                                        product!.variant,
                                        style: TextStyle(
                                          color: Colors.grey[400],
                                          fontSize: 14,
                                        ),
                                      ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Product ID: ${item.productId}',
                                      style: TextStyle(
                                        color: Colors.grey[400],
                                        fontSize: 12,
                                      ),
                                    ),
                                    Text(
                                      'Price: ₹${item.productPrice}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                      ),
                                    ),
                                    Text(
                                      'Quantity: ${item.quantity}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                      ),
                                    ),
                                    Text(
                                      'Total: ₹${item.totalPrice}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ] else ...[
                      const Text('No items found in this order'),
                    ],
                    const Divider(color: Colors.white24),
                    // Order Summary
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Total Amount:'),
                        Text(
                          '₹${_order.totalAmount}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Payment Method:'),
                        Text(_order.paymentMethod),
                      ],
                    ),
                    // Payment Information Section
                    if (_order.paymentMethod.toLowerCase() == 'razorpay' &&
                        (_order.paymentId != null || _order.razorpayOrderId != null)) ...[
                      const Divider(color: Colors.white24),
                      const SizedBox(height: 8),
                      const Text(
                        'Payment Information',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_order.paymentId != null) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Payment ID:'),
                            Flexible(
                              child: Text(
                                _order.paymentId!,
                                style: const TextStyle(fontFamily: 'monospace'),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                      ],
                      if (_order.razorpayOrderId != null) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Razorpay Order ID:'),
                            Flexible(
                              child: Text(
                                _order.razorpayOrderId!,
                                style: const TextStyle(fontFamily: 'monospace'),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                      ],
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Payment Verified:'),
                          Row(
                            children: [
                              Icon(
                                _order.isPaymentVerified ? Icons.check_circle : Icons.warning,
                                color: _order.isPaymentVerified ? Colors.green : Colors.orange,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                _order.isPaymentVerified ? 'Verified' : 'Unverified',
                                style: TextStyle(
                                  color: _order.isPaymentVerified ? Colors.green : Colors.orange,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      if (!_order.isPaymentVerified && _order.paymentId != null && _order.paymentSignature != null) ...[
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () => _verifyPayment(),
                          icon: const Icon(Icons.verified),
                          label: const Text('Verify Payment'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Verify payment using PaymentService
  Future<void> _verifyPayment() async {
    try {
      if (_order.paymentId == null || _order.razorpayOrderId == null || _order.paymentSignature == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Missing payment information for verification'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Verifying payment...'),
            ],
          ),
        ),
      );

      final success = await _paymentService.verifyAndUpdatePayment(
        orderId: _order.razorpayOrderId!,
        paymentId: _order.paymentId!,
        signature: _order.paymentSignature!,
      );

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        if (success) {
          // Update the order in the UI
          setState(() {
            _order = _order.copyWith(isPaymentVerified: true);
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment verified successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment verification failed'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      _logger.e('Error verifying payment: $e');
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error verifying payment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Copy address to clipboard
  Future<void> _copyAddressToClipboard(String address) async {
    try {
      await Clipboard.setData(ClipboardData(text: address));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Address copied to clipboard'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      _logger.e('Error copying address to clipboard: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to copy address'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // Parse and save address
  Future<void> _parseAndSaveAddress(String addressString) async {
    try {
      EasyLoading.show(status: 'Parsing and saving address...');

      // Parse and save the address
      final addressModel = await _addressService.parseAndSaveAddress(addressString, _order.userId);

      if (addressModel != null) {
        setState(() {
          _addressModel = addressModel;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Address parsed and saved successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to parse and save address'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      _logger.e('Error parsing and saving address: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error parsing and saving address: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      EasyLoading.dismiss();
    }
  }

  // Open map with address
  Future<void> _openMapWithAddress(String address) async {
    final encodedAddress = Uri.encodeComponent(address);
    final url = 'https://www.google.com/maps/search/?api=1&query=$encodedAddress';
    final uri = Uri.parse(url);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Opening map for delivery address'),
        duration: const Duration(seconds: 2),
      ),
    );

    try {
      await launchUrl(uri);
    } catch (e) {
      _logger.e('Error launching map: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open map: $e')),
        );
      }
    }
  }

  Widget _buildStatusChip(OrderStatus status) {
    Color color;
    String label;

    switch (status) {
      case OrderStatus.pending:
        color = Colors.orange;
        label = 'Pending';
        break;
      case OrderStatus.processing:
        color = Colors.blue;
        label = 'Processing';
        break;
      case OrderStatus.shipped:
        color = Colors.purple;
        label = 'Shipped';
        break;
      case OrderStatus.delivered:
        color = Colors.green;
        label = 'Delivered';
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        label = 'Cancelled';
        break;
      case OrderStatus.returned:
        color = Colors.amber;
        label = 'Returned';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withAlpha(51), // 0.2 opacity (51/255)
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Text(
        label,
        style: TextStyle(color: color),
      ),
    );
  }
}
