import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../models/address_model.dart';
import '../services/address_service.dart';

class AddressMapView extends StatefulWidget {
  final String address;
  final AddressModel? addressModel;
  final double height;
  final double? latitude;
  final double? longitude;
  final bool showInfoWindow;

  const AddressMapView({
    super.key,
    required this.address,
    this.addressModel,
    this.height = 200,
    this.latitude,
    this.longitude,
    this.showInfoWindow = true,
  });

  @override
  State<AddressMapView> createState() => _AddressMapViewState();
}

class _AddressMapViewState extends State<AddressMapView> {
  final AddressService _addressService = AddressService();
  GoogleMapController? _mapController;

  bool _isLoading = true;
  LatLng? _coordinates;
  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _initializeMap() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // If coordinates are provided directly, use them
      if (widget.latitude != null && widget.longitude != null) {
        _coordinates = LatLng(widget.latitude!, widget.longitude!);
      }
      // If an address model is provided with coordinates, use them
      else if (widget.addressModel != null &&
               widget.addressModel!.latitude != null &&
               widget.addressModel!.longitude != null) {
        _coordinates = LatLng(
          widget.addressModel!.latitude!,
          widget.addressModel!.longitude!,
        );
      }
      // Otherwise, geocode the address string
      else {
        final coordinates = await _addressService.getCoordinatesFromAddress(widget.address);
        if (coordinates != null) {
          _coordinates = LatLng(coordinates['lat']!, coordinates['lng']!);
        }
      }

      // Create marker if coordinates were found
      if (_coordinates != null) {
        _markers = {
          Marker(
            markerId: const MarkerId('address'),
            position: _coordinates!,
            infoWindow: widget.showInfoWindow
                ? InfoWindow(
                    title: widget.addressModel?.name ?? 'Delivery Address',
                    snippet: widget.address,
                  )
                : InfoWindow.noText,
          ),
        };
      }
    } catch (e) {
      debugPrint('Error initializing map: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;

    // Note: setMapStyle is deprecated but still works
    // The recommended GoogleMap.style property is not yet available in all versions
    // We'll keep using setMapStyle for now
    _mapController?.setMapStyle('''
      [
        {
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#212121"
            }
          ]
        },
        {
          "elementType": "labels.icon",
          "stylers": [
            {
              "visibility": "off"
            }
          ]
        },
        {
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#757575"
            }
          ]
        },
        {
          "elementType": "labels.text.stroke",
          "stylers": [
            {
              "color": "#212121"
            }
          ]
        },
        {
          "featureType": "administrative",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#757575"
            }
          ]
        },
        {
          "featureType": "administrative.country",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#9e9e9e"
            }
          ]
        },
        {
          "featureType": "administrative.locality",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#bdbdbd"
            }
          ]
        },
        {
          "featureType": "poi",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#757575"
            }
          ]
        },
        {
          "featureType": "poi.park",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#181818"
            }
          ]
        },
        {
          "featureType": "poi.park",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#616161"
            }
          ]
        },
        {
          "featureType": "poi.park",
          "elementType": "labels.text.stroke",
          "stylers": [
            {
              "color": "#1b1b1b"
            }
          ]
        },
        {
          "featureType": "road",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#2c2c2c"
            }
          ]
        },
        {
          "featureType": "road",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#8a8a8a"
            }
          ]
        },
        {
          "featureType": "road.arterial",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#373737"
            }
          ]
        },
        {
          "featureType": "road.highway",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#3c3c3c"
            }
          ]
        },
        {
          "featureType": "road.highway.controlled_access",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#4e4e4e"
            }
          ]
        },
        {
          "featureType": "road.local",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#616161"
            }
          ]
        },
        {
          "featureType": "transit",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#757575"
            }
          ]
        },
        {
          "featureType": "water",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#000000"
            }
          ]
        },
        {
          "featureType": "water",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#3d3d3d"
            }
          ]
        }
      ]
    ''');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return SizedBox(
        height: widget.height,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_coordinates == null) {
      return SizedBox(
        height: widget.height,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.location_off,
              color: Colors.white70,
              size: 32,
            ),
            const SizedBox(height: 8),
            const Text(
              'Could not locate address on map',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _initializeMap,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    return SizedBox(
      height: widget.height,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition: CameraPosition(
            target: _coordinates!,
            zoom: 15,
          ),
          markers: _markers,
          mapType: MapType.normal,
          myLocationEnabled: false,
          zoomControlsEnabled: true,
          zoomGesturesEnabled: true,
          compassEnabled: true,
        ),
      ),
    );
  }
}
