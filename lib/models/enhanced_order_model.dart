import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/shared_constants.dart';

// Enhanced Order Model that supports both single-item and multi-item orders
class EnhancedOrderModel {
  final String id;
  final String userId;
  final List<OrderItem> items; // Support multiple items
  final String orderDate;
  final String status;
  final String? shippingAddress;
  final String? trackingNumber;
  final DateTime? shippedDate;
  final DateTime? deliveredDate;
  final String? cancelReason;
  final String? returnReason;
  final num totalAmount;
  final String paymentMethod;
  final bool isPaid;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? customerName;
  final String? estimatedDelivery;
  final String? notes;

  EnhancedOrderModel({
    required this.id,
    required this.userId,
    required this.items,
    required this.orderDate,
    required this.status,
    this.shippingAddress,
    this.trackingNumber,
    this.shippedDate,
    this.deliveredDate,
    this.cancelReason,
    this.returnReason,
    required this.totalAmount,
    required this.paymentMethod,
    required this.isPaid,
    required this.createdAt,
    this.updatedAt,
    this.customerName,
    this.estimatedDelivery,
    this.notes,
  });

  // Convert from legacy single-item format (Admin app)
  factory EnhancedOrderModel.fromLegacySingleItem({
    required String id,
    required String userId,
    required String productId,
    required String orderDate,
    required String status,
    String? shippingAddress,
    String? trackingNumber,
    DateTime? shippedDate,
    DateTime? deliveredDate,
    String? cancelReason,
    String? returnReason,
    required num totalAmount,
    required int quantity,
    required String paymentMethod,
    required bool isPaid,
    required DateTime createdAt,
    DateTime? updatedAt,
  }) {
    return EnhancedOrderModel(
      id: id,
      userId: userId,
      items: [
        OrderItem(
          id: '${id}_item_1',
          productId: productId,
          productName: 'Product $productId', // Will be populated from product data
          productImage: null,
          productPrice: totalAmount / quantity,
          quantity: quantity,
          totalPrice: totalAmount,
        ),
      ],
      orderDate: orderDate,
      status: status,
      shippingAddress: shippingAddress,
      trackingNumber: trackingNumber,
      shippedDate: shippedDate,
      deliveredDate: deliveredDate,
      cancelReason: cancelReason,
      returnReason: returnReason,
      totalAmount: totalAmount,
      paymentMethod: paymentMethod,
      isPaid: isPaid,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Convert from multi-item format (User app)
  factory EnhancedOrderModel.fromMap(Map<String, dynamic> map, String id) {
    try {
      // Parse items - handle both single item and multi-item formats
      List<OrderItem> items = [];
      
      // Check for multi-item format first
      if (map.containsKey('order_items') && map['order_items'] is List) {
        final itemsData = map['order_items'] as List<dynamic>;
        for (final itemData in itemsData) {
          if (itemData is Map<String, dynamic>) {
            items.add(OrderItem.fromMap(itemData));
          }
        }
      } else if (map.containsKey('items') && map['items'] is List) {
        final itemsData = map['items'] as List<dynamic>;
        for (final itemData in itemsData) {
          if (itemData is Map<String, dynamic>) {
            items.add(OrderItem.fromMap(itemData));
          }
        }
      } else {
        // Single item format (Admin app) - convert to multi-item
        final productId = map[OrderFields.productId] ?? map[OrderFields.productIdAlt] ?? '';
        final quantity = map[OrderFields.quantity] ?? 1;
        final totalAmount = map[OrderFields.totalAmount] ?? map[OrderFields.totalAmountAlt] ?? 0;
        
        if (productId.isNotEmpty) {
          items.add(OrderItem(
            id: '${id}_item_1',
            productId: productId,
            productName: 'Product $productId',
            productImage: null,
            productPrice: totalAmount / quantity,
            quantity: quantity,
            totalPrice: totalAmount,
          ));
        }
      }

      return EnhancedOrderModel(
        id: id,
        userId: map[OrderFields.userId] ?? map[OrderFields.userIdAlt] ?? '',
        items: items,
        orderDate: map[OrderFields.orderDate] ?? map[OrderFields.orderDateAlt] ?? '',
        status: map[OrderFields.status] ?? OrderStatus.pending,
        shippingAddress: map[OrderFields.shippingAddress] ?? map[OrderFields.shippingAddressAlt],
        trackingNumber: map[OrderFields.trackingNumber] ?? map[OrderFields.trackingNumberAlt],
        shippedDate: map[OrderFields.shippedDate]?.toDate() ?? map[OrderFields.shippedDateAlt]?.toDate(),
        deliveredDate: map[OrderFields.deliveredDate]?.toDate() ?? map[OrderFields.deliveredDateAlt]?.toDate(),
        cancelReason: map[OrderFields.cancelReason] ?? map[OrderFields.cancelReasonAlt],
        returnReason: map[OrderFields.returnReason] ?? map[OrderFields.returnReasonAlt],
        totalAmount: map[OrderFields.totalAmount] ?? map[OrderFields.totalAmountAlt] ?? 0,
        paymentMethod: map[OrderFields.paymentMethod] ?? map[OrderFields.paymentMethodAlt] ?? PaymentMethods.cashOnDelivery,
        isPaid: map[OrderFields.isPaid] ?? map[OrderFields.isPaidAlt] ?? false,
        createdAt: (map[OrderFields.createdAt] as Timestamp?)?.toDate() ?? 
                   (map[OrderFields.createdAtAlt] as Timestamp?)?.toDate() ?? 
                   DateTime.now(),
        updatedAt: (map[OrderFields.updatedAt] as Timestamp?)?.toDate() ?? 
                   (map[OrderFields.updatedAtAlt] as Timestamp?)?.toDate(),
        customerName: map[OrderFields.customerName],
        estimatedDelivery: map[OrderFields.estimatedDelivery],
        notes: map[OrderFields.notes],
      );
    } catch (e) {
      // Return minimal valid order on error
      return EnhancedOrderModel(
        id: id,
        userId: '',
        items: [],
        orderDate: DateTime.now().toIso8601String(),
        status: OrderStatus.pending,
        totalAmount: 0,
        paymentMethod: PaymentMethods.cashOnDelivery,
        isPaid: false,
        createdAt: DateTime.now(),
      );
    }
  }

  Map<String, dynamic> toMap() {
    final itemsData = items.map((item) => item.toMap()).toList();

    return {
      // Multi-item format (User app)
      'order_items': itemsData,
      'items': itemsData, // Alternative field name
      
      // Include both field name formats for compatibility
      OrderFields.userId: userId,
      OrderFields.userIdAlt: userId,
      OrderFields.orderDate: orderDate,
      OrderFields.orderDateAlt: orderDate,
      OrderFields.status: status,
      OrderFields.shippingAddress: shippingAddress,
      OrderFields.shippingAddressAlt: shippingAddress,
      OrderFields.trackingNumber: trackingNumber,
      OrderFields.trackingNumberAlt: trackingNumber,
      OrderFields.shippedDate: shippedDate != null ? Timestamp.fromDate(shippedDate!) : null,
      OrderFields.shippedDateAlt: shippedDate != null ? Timestamp.fromDate(shippedDate!) : null,
      OrderFields.deliveredDate: deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null,
      OrderFields.deliveredDateAlt: deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null,
      OrderFields.cancelReason: cancelReason,
      OrderFields.cancelReasonAlt: cancelReason,
      OrderFields.returnReason: returnReason,
      OrderFields.returnReasonAlt: returnReason,
      OrderFields.totalAmount: totalAmount,
      OrderFields.totalAmountAlt: totalAmount,
      OrderFields.paymentMethod: paymentMethod,
      OrderFields.paymentMethodAlt: paymentMethod,
      OrderFields.isPaid: isPaid,
      OrderFields.isPaidAlt: isPaid,
      OrderFields.createdAt: Timestamp.fromDate(createdAt),
      OrderFields.createdAtAlt: Timestamp.fromDate(createdAt),
      OrderFields.updatedAt: updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      OrderFields.updatedAtAlt: updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      OrderFields.customerName: customerName,
      OrderFields.estimatedDelivery: estimatedDelivery,
      OrderFields.notes: notes,
      
      // Legacy single-item fields for backward compatibility
      if (items.isNotEmpty) OrderFields.productId: items.first.productId,
      if (items.isNotEmpty) OrderFields.productIdAlt: items.first.productId,
      if (items.isNotEmpty) OrderFields.quantity: items.first.quantity,
    };
  }

  // Get total quantity across all items
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);
  
  // Check if order has multiple items
  bool get isMultiItem => items.length > 1;
  
  // Get first item (for backward compatibility)
  OrderItem? get firstItem => items.isNotEmpty ? items.first : null;
}

// Order Item Model
class OrderItem {
  final String id;
  final String productId;
  final String productName;
  final String? productImage;
  final double productPrice;
  final int quantity;
  final double totalPrice;

  OrderItem({
    required this.id,
    required this.productId,
    required this.productName,
    this.productImage,
    required this.productPrice,
    required this.quantity,
    required this.totalPrice,
  });

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      id: map['id'] ?? map['item_id'] ?? '',
      productId: map['product_id'] ?? map['productId'] ?? '',
      productName: map['product_name'] ?? map['productName'] ?? '',
      productImage: map['product_image'] ?? map['productImage'],
      productPrice: (map['product_price'] ?? map['productPrice'] ?? 0).toDouble(),
      quantity: map['quantity'] ?? 1,
      totalPrice: (map['total_price'] ?? map['totalPrice'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': id,
      'product_id': productId,
      'productId': productId,
      'product_name': productName,
      'productName': productName,
      'product_image': productImage,
      'productImage': productImage,
      'product_price': productPrice,
      'productPrice': productPrice,
      'quantity': quantity,
      'total_price': totalPrice,
      'totalPrice': totalPrice,
    };
  }
}
