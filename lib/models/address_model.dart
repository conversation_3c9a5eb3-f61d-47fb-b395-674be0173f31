import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class AddressModel {
  final String id;
  final String userId;
  final String name; // Name of the person at this address
  final String addressLine1;
  final String? addressLine2;
  final String city;
  final String state;
  final String postalCode;
  final String country;
  final String phoneNumber;
  final bool isDefault;
  final double? latitude;
  final double? longitude;
  final DateTime createdAt;
  final DateTime updatedAt;

  AddressModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.addressLine1,
    this.addressLine2,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    required this.phoneNumber,
    this.isDefault = false,
    this.latitude,
    this.longitude,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AddressModel.fromMap(Map<String, dynamic> map, String id) {
    return AddressModel(
      id: id,
      userId: map['userId'] ?? '',
      name: map['name'] ?? '',
      addressLine1: map['addressLine1'] ?? '',
      addressLine2: map['addressLine2'],
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      postalCode: map['postalCode'] ?? '',
      country: map['country'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      isDefault: map['isDefault'] ?? false,
      latitude: map['latitude'],
      longitude: map['longitude'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'name': name,
      'addressLine1': addressLine1,
      'addressLine2': addressLine2,
      'city': city,
      'state': state,
      'postalCode': postalCode,
      'country': country,
      'phoneNumber': phoneNumber,
      'isDefault': isDefault,
      'latitude': latitude,
      'longitude': longitude,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  AddressModel copyWith({
    String? userId,
    String? name,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    String? phoneNumber,
    bool? isDefault,
    double? latitude,
    double? longitude,
    DateTime? updatedAt,
  }) {
    return AddressModel(
      id: id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      city: city ?? this.city,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isDefault: isDefault ?? this.isDefault,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Get a formatted string representation of the address
  String get formattedAddress {
    final parts = [
      addressLine1,
      if (addressLine2 != null && addressLine2!.isNotEmpty) addressLine2,
      city,
      state,
      postalCode,
      country,
    ];
    return parts.join(', ');
  }

  /// Parse a formatted address string into an AddressModel
  /// This is a best-effort parsing and may not be accurate for all address formats
  static AddressModel? parseFromString(String addressString, String userId) {
    try {
      debugPrint('Parsing address: $addressString');

      // Clean up the address string
      String cleanAddress = addressString.trim();

      // Handle different address formats

      // Case 1: Comma-separated address (most common)
      if (cleanAddress.contains(',')) {
        // Split the address string by commas
        final parts = cleanAddress.split(',').map((e) => e.trim()).toList();
        debugPrint('Address parts: $parts');

        // Need at least 2 parts for a minimal address
        if (parts.isEmpty) {
          debugPrint('Address has no parts after splitting');
          return _createSimpleAddress(cleanAddress, userId);
        }

        // Extract parts based on position
        final addressLine1 = parts[0];
        String? addressLine2;
        String city;
        String state;
        String country;
        String postalCode = '';

        if (parts.length >= 4) {
          // Longer address format
          addressLine2 = parts.length > 4 ? parts[1] : null;
          city = parts.length > 4 ? parts[2] : parts[1];
          state = parts.length > 4 ? parts[3] : parts[2];
          country = parts.last;

          // Try to extract postal code from state part
          final postalRegex = RegExp(r'\b\d{5,6}\b');
          final postalMatch = postalRegex.firstMatch(state);
          if (postalMatch != null) {
            postalCode = postalMatch.group(0) ?? '';
            state = state.replaceAll(postalRegex, '').trim();
          } else {
            // If no postal code in state part, check other parts
            for (final part in parts) {
              final match = postalRegex.firstMatch(part);
              if (match != null) {
                postalCode = match.group(0) ?? '';
                break;
              }
            }
          }
        } else {
          // Shorter address format, do our best
          city = parts.length > 1 ? parts[1] : '';
          state = parts.length > 2 ? parts[2] : '';
          country = parts.length > 3 ? parts[3] : 'India'; // Default to India
        }

        return AddressModel(
          id: '', // This will be assigned when saved to Firestore
          userId: userId,
          name: '', // We don't have this information from the string
          addressLine1: addressLine1,
          addressLine2: addressLine2,
          city: city,
          state: state,
          postalCode: postalCode,
          country: country,
          phoneNumber: '', // We don't have this information from the string
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }
      // Case 2: Space-separated address without commas
      else {
        return _createSimpleAddress(cleanAddress, userId);
      }
    } catch (e) {
      debugPrint('Error parsing address string: $e');
      return _createSimpleAddress(addressString, userId);
    }
  }

  /// Create a simple address model when parsing fails
  static AddressModel _createSimpleAddress(String addressString, String userId) {
    debugPrint('Creating simple address from: $addressString');
    return AddressModel(
      id: '', // This will be assigned when saved to Firestore
      userId: userId,
      name: '', // We don't have this information from the string
      addressLine1: addressString,
      addressLine2: null,
      city: '',
      state: '',
      postalCode: '',
      country: 'India', // Default to India
      phoneNumber: '', // We don't have this information from the string
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
