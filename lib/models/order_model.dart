import 'package:cloud_firestore/cloud_firestore.dart';

import '../constants/app_constants.dart';

class OrderModel {
  final String id;
  final String userId;
  final List<OrderItem> items; // Support multiple items
  final String orderDate;
  final OrderStatus status;
  final String? shippingAddress;
  final String? trackingNumber;
  final DateTime? shippedDate;
  final DateTime? deliveredDate;
  final String? cancelReason;
  final String? returnReason;
  final num totalAmount;
  final String paymentMethod;
  final bool isPaid;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? customerName;
  final String? estimatedDelivery;
  final String? notes;

  // Legacy fields for backward compatibility
  String get productId => items.isNotEmpty ? items.first.productId : '';
  int get quantity => items.fold<int>(0, (total, item) => total + item.quantity);

  // Additional helper methods
  bool get isMultiItem => items.length > 1;
  OrderItem? get firstItem => items.isNotEmpty ? items.first : null;

  OrderModel({
    required this.id,
    required this.userId,
    required this.items,
    required this.orderDate,
    required this.status,
    this.shippingAddress,
    this.trackingNumber,
    this.shippedDate,
    this.deliveredDate,
    this.cancelReason,
    this.returnReason,
    required this.totalAmount,
    required this.paymentMethod,
    required this.isPaid,
    required this.createdAt,
    this.updatedAt,
    this.customerName,
    this.estimatedDelivery,
    this.notes,
  });

  // Constructor for legacy single-item format
  OrderModel.fromSingleItem({
    required this.id,
    required this.userId,
    required String productId,
    required this.orderDate,
    required this.status,
    this.shippingAddress,
    this.trackingNumber,
    this.shippedDate,
    this.deliveredDate,
    this.cancelReason,
    this.returnReason,
    required this.totalAmount,
    required int quantity,
    required this.paymentMethod,
    required this.isPaid,
    required this.createdAt,
    this.updatedAt,
    this.customerName,
    this.estimatedDelivery,
    this.notes,
  }) : items = [
          OrderItem(
            id: '${id}_item_1',
            productId: productId,
            productName: 'Product $productId',
            productImage: null,
            productPrice: (totalAmount / quantity).toDouble(),
            quantity: quantity,
            totalPrice: totalAmount.toDouble(),
          ),
        ];

  Map<String, dynamic> toMap() {
    final itemsData = items.map((item) => item.toMap()).toList();

    return {
      // Multi-item format (User app)
      'order_items': itemsData,
      'items': itemsData, // Alternative field name

      // Include both field name formats for maximum compatibility
      'userId': userId,
      'user_id': userId, // User app format
      'orderDate': orderDate,
      'order_date': orderDate, // User app format
      'status': status.toString().split('.').last,
      'shippingAddress': shippingAddress,
      'shipping_address': shippingAddress, // User app format
      'trackingNumber': trackingNumber,
      'tracking_number': trackingNumber, // User app format
      'shippedDate': shippedDate != null ? Timestamp.fromDate(shippedDate!) : null,
      'shipped_date': shippedDate != null ? Timestamp.fromDate(shippedDate!) : null, // User app format
      'deliveredDate': deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null,
      'delivered_date': deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null, // User app format
      'cancelReason': cancelReason,
      'cancel_reason': cancelReason, // User app format
      'returnReason': returnReason,
      'return_reason': returnReason, // User app format
      'totalAmount': totalAmount,
      'total_amount': totalAmount, // User app format
      'paymentMethod': paymentMethod,
      'payment_method': paymentMethod, // User app format
      'isPaid': isPaid,
      'is_paid': isPaid, // User app format
      'createdAt': Timestamp.fromDate(createdAt),
      'created_at': Timestamp.fromDate(createdAt), // User app format
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'updated_at': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null, // User app format
      'customerName': customerName,
      'estimatedDelivery': estimatedDelivery,
      'notes': notes,

      // Legacy single-item fields for backward compatibility
      if (items.isNotEmpty) 'productId': items.first.productId,
      if (items.isNotEmpty) 'product_uid': items.first.productId,
      if (items.isNotEmpty) 'quantity': quantity,
    };
  }

  factory OrderModel.fromMap(Map<String, dynamic> map, String id) {
    try {
      // Parse items - handle both single item and multi-item formats
      List<OrderItem> items = [];

      // Check for multi-item format first
      if (map.containsKey('order_items') && map['order_items'] is List) {
        final itemsData = map['order_items'] as List<dynamic>;
        for (final itemData in itemsData) {
          if (itemData is Map<String, dynamic>) {
            items.add(OrderItem.fromMap(itemData));
          }
        }
      } else if (map.containsKey('items') && map['items'] is List) {
        final itemsData = map['items'] as List<dynamic>;
        for (final itemData in itemsData) {
          if (itemData is Map<String, dynamic>) {
            items.add(OrderItem.fromMap(itemData));
          }
        }
      } else {
        // Single item format (Admin app) - convert to multi-item
        final productId = map['productId'] ?? map['product_uid'] ?? '';
        final quantity = map['quantity'] ?? 1;
        final totalAmount = map['totalAmount'] ?? map['total_amount'] ?? 0;

        if (productId.isNotEmpty) {
          items.add(OrderItem(
            id: '${id}_item_1',
            productId: productId,
            productName: 'Product $productId',
            productImage: null,
            productPrice: (totalAmount / quantity).toDouble(),
            quantity: quantity,
            totalPrice: totalAmount.toDouble(),
          ));
        }
      }

      return OrderModel(
        id: id,
        // Handle both userId and user_id field names
        userId: map['userId'] ?? map['user_id'] ?? '',
        items: items,
        // Handle both orderDate and order_date field names
        orderDate: map['orderDate'] ?? map['order_date'] ?? '',
        status: _stringToOrderStatus(map['status'] ?? 'pending'),
        // Handle both shippingAddress and shipping_address field names
        shippingAddress: map['shippingAddress'] ?? map['shipping_address'],
        // Handle both trackingNumber and tracking_number field names
        trackingNumber: map['trackingNumber'] ?? map['tracking_number'],
        shippedDate: map['shippedDate']?.toDate(),
        deliveredDate: map['deliveredDate']?.toDate(),
        cancelReason: map['cancelReason'] ?? map['cancel_reason'],
        returnReason: map['returnReason'] ?? map['return_reason'],
        totalAmount: map['totalAmount'] ?? map['total_amount'] ?? 0,
        // Handle both paymentMethod and payment_method field names
        paymentMethod: map['paymentMethod'] ?? map['payment_method'] ?? '',
        // Handle both isPaid and is_paid field names
        isPaid: map['isPaid'] ?? map['is_paid'] ?? false,
        createdAt: (map['createdAt'] as Timestamp?)?.toDate() ??
                   (map['created_at'] as Timestamp?)?.toDate() ??
                   DateTime.now(),
        updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ??
                   (map['updated_at'] as Timestamp?)?.toDate(),
        customerName: map['customerName'],
        estimatedDelivery: map['estimatedDelivery'],
        notes: map['notes'],
      );
    } catch (e) {
      // Return minimal valid order on error
      return OrderModel(
        id: id,
        userId: '',
        items: [],
        orderDate: DateTime.now().toIso8601String(),
        status: OrderStatus.pending,
        totalAmount: 0,
        paymentMethod: '',
        isPaid: false,
        createdAt: DateTime.now(),
      );
    }
  }

  static OrderStatus _stringToOrderStatus(String status) {
    switch (status) {
      case 'pending':
        return OrderStatus.pending;
      case 'processing':
        return OrderStatus.processing;
      case 'shipped':
        return OrderStatus.shipped;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      case 'returned':
        return OrderStatus.returned;
      default:
        return OrderStatus.pending;
    }
  }

  // Create a copy of this order with some fields updated
  OrderModel copyWith({
    String? id,
    String? userId,
    List<OrderItem>? items,
    String? orderDate,
    OrderStatus? status,
    String? shippingAddress,
    String? trackingNumber,
    DateTime? shippedDate,
    DateTime? deliveredDate,
    String? cancelReason,
    String? returnReason,
    num? totalAmount,
    String? paymentMethod,
    bool? isPaid,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? customerName,
    String? estimatedDelivery,
    String? notes,
  }) {
    return OrderModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      items: items ?? this.items,
      orderDate: orderDate ?? this.orderDate,
      status: status ?? this.status,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      shippedDate: shippedDate ?? this.shippedDate,
      deliveredDate: deliveredDate ?? this.deliveredDate,
      cancelReason: cancelReason ?? this.cancelReason,
      returnReason: returnReason ?? this.returnReason,
      totalAmount: totalAmount ?? this.totalAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      isPaid: isPaid ?? this.isPaid,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      customerName: customerName ?? this.customerName,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      notes: notes ?? this.notes,
    );
  }
}

// Order Item Model for multi-item support
class OrderItem {
  final String id;
  final String productId;
  final String productName;
  final String? productImage;
  final double productPrice;
  final int quantity;
  final double totalPrice;

  OrderItem({
    required this.id,
    required this.productId,
    required this.productName,
    this.productImage,
    required this.productPrice,
    required this.quantity,
    required this.totalPrice,
  });

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      id: map['id'] ?? map['item_id'] ?? '',
      productId: map['product_id'] ?? map['productId'] ?? '',
      productName: map['product_name'] ?? map['productName'] ?? '',
      productImage: map['product_image'] ?? map['productImage'],
      productPrice: (map['product_price'] ?? map['productPrice'] ?? 0).toDouble(),
      quantity: map['quantity'] ?? 1,
      totalPrice: (map['total_price'] ?? map['totalPrice'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': id,
      'product_id': productId,
      'productId': productId,
      'product_name': productName,
      'productName': productName,
      'product_image': productImage,
      'productImage': productImage,
      'product_price': productPrice,
      'productPrice': productPrice,
      'quantity': quantity,
      'total_price': totalPrice,
      'totalPrice': totalPrice,
    };
  }
}
