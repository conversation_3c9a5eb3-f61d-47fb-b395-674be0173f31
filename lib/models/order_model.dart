import 'package:cloud_firestore/cloud_firestore.dart';

import '../constants/app_constants.dart';

class OrderModel {

  final String id;
  final String userId;
  final String productId;
  final String orderDate;
  final OrderStatus status;
  final String? shippingAddress;
  final String? trackingNumber;
  final DateTime? shippedDate;
  final DateTime? deliveredDate;
  final String? cancelReason;
  final String? returnReason;
  final num totalAmount;
  final int quantity;
  final String paymentMethod;
  final bool isPaid;
  final DateTime createdAt;
  final DateTime? updatedAt;

  OrderModel({
    required this.id,
    required this.userId,
    required this.productId,
    required this.orderDate,
    required this.status,
    this.shippingAddress,
    this.trackingNumber,
    this.shippedDate,
    this.deliveredDate,
    this.cancelReason,
    this.returnReason,
    required this.totalAmount,
    required this.quantity,
    required this.paymentMethod,
    required this.isPaid,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      // Include both field name formats for maximum compatibility
      'userId': userId,
      'user_id': userId, // User app format
      'productId': productId,
      'product_uid': productId, // User app format
      'orderDate': orderDate,
      'order_date': orderDate, // User app format
      'status': status.toString().split('.').last,
      'shippingAddress': shippingAddress,
      'shipping_address': shippingAddress, // User app format
      'trackingNumber': trackingNumber,
      'tracking_number': trackingNumber, // User app format
      'shippedDate': shippedDate != null ? Timestamp.fromDate(shippedDate!) : null,
      'shipped_date': shippedDate != null ? Timestamp.fromDate(shippedDate!) : null, // User app format
      'deliveredDate': deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null,
      'delivered_date': deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null, // User app format
      'cancelReason': cancelReason,
      'cancel_reason': cancelReason, // User app format
      'returnReason': returnReason,
      'return_reason': returnReason, // User app format
      'totalAmount': totalAmount,
      'total_amount': totalAmount, // User app format
      'quantity': quantity,
      'paymentMethod': paymentMethod,
      'payment_method': paymentMethod, // User app format
      'isPaid': isPaid,
      'is_paid': isPaid, // User app format
      'createdAt': Timestamp.fromDate(createdAt),
      'created_at': Timestamp.fromDate(createdAt), // User app format
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'updated_at': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null, // User app format
    };
  }

  factory OrderModel.fromMap(Map<String, dynamic> map, String id) {
    return OrderModel(
      id: id,
      // Handle both userId and user_id field names
      userId: map['userId'] ?? map['user_id'] ?? '',
      // Handle both productId and product_uid field names
      productId: map['productId'] ?? map['product_uid'] ?? '',
      // Handle both orderDate and order_date field names
      orderDate: map['orderDate'] ?? map['order_date'] ?? '',
      status: _stringToOrderStatus(map['status'] ?? 'pending'),
      // Handle both shippingAddress and shipping_address field names
      shippingAddress: map['shippingAddress'] ?? map['shipping_address'],
      // Handle both trackingNumber and tracking_number field names
      trackingNumber: map['trackingNumber'] ?? map['tracking_number'],
      shippedDate: map['shippedDate']?.toDate(),
      deliveredDate: map['deliveredDate']?.toDate(),
      cancelReason: map['cancelReason'] ?? map['cancel_reason'],
      returnReason: map['returnReason'] ?? map['return_reason'],
      totalAmount: map['totalAmount'] ?? map['total_amount'] ?? 0,
      quantity: map['quantity'] ?? 1,
      // Handle both paymentMethod and payment_method field names
      paymentMethod: map['paymentMethod'] ?? map['payment_method'] ?? '',
      // Handle both isPaid and is_paid field names
      isPaid: map['isPaid'] ?? map['is_paid'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ??
                 (map['created_at'] as Timestamp?)?.toDate() ??
                 DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ??
                 (map['updated_at'] as Timestamp?)?.toDate(),
    );
  }

  static OrderStatus _stringToOrderStatus(String status) {
    switch (status) {
      case 'pending':
        return OrderStatus.pending;
      case 'processing':
        return OrderStatus.processing;
      case 'shipped':
        return OrderStatus.shipped;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      case 'returned':
        return OrderStatus.returned;
      default:
        return OrderStatus.pending;
    }
  }

  // Create a copy of this order with some fields updated
  OrderModel copyWith({
    String? id,
    String? userId,
    String? productId,
    String? orderDate,
    OrderStatus? status,
    String? shippingAddress,
    String? trackingNumber,
    DateTime? shippedDate,
    DateTime? deliveredDate,
    String? cancelReason,
    String? returnReason,
    num? totalAmount,
    int? quantity,
    String? paymentMethod,
    bool? isPaid,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrderModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      productId: productId ?? this.productId,
      orderDate: orderDate ?? this.orderDate,
      status: status ?? this.status,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      shippedDate: shippedDate ?? this.shippedDate,
      deliveredDate: deliveredDate ?? this.deliveredDate,
      cancelReason: cancelReason ?? this.cancelReason,
      returnReason: returnReason ?? this.returnReason,
      totalAmount: totalAmount ?? this.totalAmount,
      quantity: quantity ?? this.quantity,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      isPaid: isPaid ?? this.isPaid,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
