import 'package:cloud_firestore/cloud_firestore.dart';

import '../constants/app_constants.dart';

class OrderModel {

  final String id;
  final String userId;
  final String productId;
  final String orderDate;
  final OrderStatus status;
  final String? shippingAddress;
  final String? trackingNumber;
  final DateTime? shippedDate;
  final DateTime? deliveredDate;
  final String? cancelReason;
  final String? returnReason;
  final num totalAmount;
  final int quantity;
  final String paymentMethod;
  final bool isPaid;
  final DateTime createdAt;
  final DateTime? updatedAt;

  OrderModel({
    required this.id,
    required this.userId,
    required this.productId,
    required this.orderDate,
    required this.status,
    this.shippingAddress,
    this.trackingNumber,
    this.shippedDate,
    this.deliveredDate,
    this.cancelReason,
    this.returnReason,
    required this.totalAmount,
    required this.quantity,
    required this.paymentMethod,
    required this.isPaid,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'productId': productId,
      'orderDate': orderDate,
      'status': status.toString().split('.').last,
      'shippingAddress': shippingAddress,
      'trackingNumber': trackingNumber,
      'shippedDate': shippedDate != null ? Timestamp.fromDate(shippedDate!) : null,
      'deliveredDate': deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null,
      'cancelReason': cancelReason,
      'returnReason': returnReason,
      'totalAmount': totalAmount,
      'quantity': quantity,
      'paymentMethod': paymentMethod,
      'isPaid': isPaid,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  factory OrderModel.fromMap(Map<String, dynamic> map, String id) {
    return OrderModel(
      id: id,
      userId: map['userId'] ?? '',
      productId: map['productId'] ?? map['product_uid'] ?? '', // handle legacy field
      orderDate: map['orderDate'] ?? map['order_date'] ?? '', // handle legacy field
      status: _stringToOrderStatus(map['status'] ?? 'pending'),
      shippingAddress: map['shippingAddress'],
      trackingNumber: map['trackingNumber'],
      shippedDate: map['shippedDate']?.toDate(),
      deliveredDate: map['deliveredDate']?.toDate(),
      cancelReason: map['cancelReason'],
      returnReason: map['returnReason'],
      totalAmount: map['totalAmount'] ?? 0,
      quantity: map['quantity'] ?? 0,
      paymentMethod: map['paymentMethod'] ?? '',
      isPaid: map['isPaid'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  static OrderStatus _stringToOrderStatus(String status) {
    switch (status) {
      case 'pending':
        return OrderStatus.pending;
      case 'processing':
        return OrderStatus.processing;
      case 'shipped':
        return OrderStatus.shipped;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      case 'returned':
        return OrderStatus.returned;
      default:
        return OrderStatus.pending;
    }
  }

  // Create a copy of this order with some fields updated
  OrderModel copyWith({
    String? id,
    String? userId,
    String? productId,
    String? orderDate,
    OrderStatus? status,
    String? shippingAddress,
    String? trackingNumber,
    DateTime? shippedDate,
    DateTime? deliveredDate,
    String? cancelReason,
    String? returnReason,
    num? totalAmount,
    int? quantity,
    String? paymentMethod,
    bool? isPaid,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrderModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      productId: productId ?? this.productId,
      orderDate: orderDate ?? this.orderDate,
      status: status ?? this.status,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      shippedDate: shippedDate ?? this.shippedDate,
      deliveredDate: deliveredDate ?? this.deliveredDate,
      cancelReason: cancelReason ?? this.cancelReason,
      returnReason: returnReason ?? this.returnReason,
      totalAmount: totalAmount ?? this.totalAmount,
      quantity: quantity ?? this.quantity,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      isPaid: isPaid ?? this.isPaid,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
