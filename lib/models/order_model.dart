import 'package:cloud_firestore/cloud_firestore.dart';

import '../constants/app_constants.dart';

class OrderModel {
  final String id;
  final String userId;
  final List<OrderItem> items; // Support multiple items
  final String orderDate;
  final OrderStatus status;
  final String? shippingAddress;
  final String? trackingNumber;
  final DateTime? shippedDate;
  final DateTime? deliveredDate;
  final String? cancelReason;
  final String? returnReason;
  final num totalAmount;
  final String paymentMethod;
  final bool isPaid;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? estimatedDelivery;
  final String? notes;
  final String? paymentId;
  final String? razorpayOrderId;
  final String? paymentSignature;
  final bool isPaymentVerified;

  // Legacy fields for backward compatibility
  String get productId => items.isNotEmpty ? items.first.productId : '';
  int get quantity => items.fold<int>(0, (total, item) => total + item.quantity);

  // Additional helper methods
  bool get isMultiItem => items.length > 1;
  OrderItem? get firstItem => items.isNotEmpty ? items.first : null;

  OrderModel({
    required this.id,
    required this.userId,
    required this.items,
    required this.orderDate,
    required this.status,
    this.shippingAddress,
    this.trackingNumber,
    this.shippedDate,
    this.deliveredDate,
    this.cancelReason,
    this.returnReason,
    required this.totalAmount,
    required this.paymentMethod,
    required this.isPaid,
    required this.createdAt,
    this.updatedAt,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.estimatedDelivery,
    this.notes,
    this.paymentId,
    this.razorpayOrderId,
    this.paymentSignature,
    this.isPaymentVerified = false,
  });

  // Constructor for legacy single-item format
  OrderModel.fromSingleItem({
    required this.id,
    required this.userId,
    required String productId,
    required this.orderDate,
    required this.status,
    this.shippingAddress,
    this.trackingNumber,
    this.shippedDate,
    this.deliveredDate,
    this.cancelReason,
    this.returnReason,
    required this.totalAmount,
    required int quantity,
    required this.paymentMethod,
    required this.isPaid,
    required this.createdAt,
    this.updatedAt,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.estimatedDelivery,
    this.notes,
    this.paymentId,
    this.razorpayOrderId,
    this.paymentSignature,
    this.isPaymentVerified = false,
  }) : items = [
          OrderItem(
            id: '${id}_item_1',
            productId: productId,
            productName: 'Product $productId',
            productImage: null,
            productPrice: (totalAmount / quantity).toDouble(),
            quantity: quantity,
            totalPrice: totalAmount.toDouble(),
          ),
        ];

  Map<String, dynamic> toMap() {
    final itemsData = items.map((item) => item.toMap()).toList();

    return {
      // Multi-item format (User app)
      'order_items': itemsData,
      'items': itemsData, // Alternative field name

      // Include both field name formats for maximum compatibility
      'userId': userId,
      'user_id': userId, // User app format
      'orderDate': orderDate,
      'order_date': orderDate, // User app format
      'status': status.toString().split('.').last,
      'shippingAddress': shippingAddress,
      'shipping_address': shippingAddress, // User app format
      'trackingNumber': trackingNumber,
      'tracking_number': trackingNumber, // User app format
      'shippedDate': shippedDate != null ? Timestamp.fromDate(shippedDate!) : null,
      'shipped_date': shippedDate != null ? Timestamp.fromDate(shippedDate!) : null, // User app format
      'deliveredDate': deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null,
      'delivered_date': deliveredDate != null ? Timestamp.fromDate(deliveredDate!) : null, // User app format
      'cancelReason': cancelReason,
      'cancel_reason': cancelReason, // User app format
      'returnReason': returnReason,
      'return_reason': returnReason, // User app format
      'totalAmount': totalAmount,
      'total_amount': totalAmount, // User app format
      'paymentMethod': paymentMethod,
      'payment_method': paymentMethod, // User app format
      'isPaid': isPaid,
      'is_paid': isPaid, // User app format
      'createdAt': Timestamp.fromDate(createdAt),
      'created_at': Timestamp.fromDate(createdAt), // User app format
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'updated_at': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null, // User app format
      'customerName': customerName,
      'customer_name': customerName, // User app format
      'customerEmail': customerEmail,
      'customer_email': customerEmail, // User app format
      'customerPhone': customerPhone,
      'customer_phone': customerPhone, // User app format
      'estimatedDelivery': estimatedDelivery,
      'estimated_delivery': estimatedDelivery, // User app format
      'notes': notes,
      // Payment information (Razorpay integration)
      'paymentId': paymentId,
      'payment_id': paymentId, // User app format
      'razorpayOrderId': razorpayOrderId,
      'razorpay_order_id': razorpayOrderId, // User app format
      'paymentSignature': paymentSignature,
      'payment_signature': paymentSignature, // User app format
      'isPaymentVerified': isPaymentVerified,
      'is_payment_verified': isPaymentVerified, // User app format
      'verified': isPaymentVerified, // Alternative format

      // Legacy single-item fields for backward compatibility
      if (items.isNotEmpty) 'productId': items.first.productId,
      if (items.isNotEmpty) 'product_uid': items.first.productId,
      if (items.isNotEmpty) 'quantity': quantity,
    };
  }

  factory OrderModel.fromMap(Map<String, dynamic> map, String id) {
    try {
      // Parse items - handle both single item and multi-item formats
      List<OrderItem> items = [];

      // Check for user app multi-item format first (order_items)
      if (map.containsKey('order_items') && map['order_items'] is List) {
        final itemsData = map['order_items'] as List<dynamic>;
        for (final itemData in itemsData) {
          if (itemData is Map<String, dynamic>) {
            items.add(OrderItem.fromMap(itemData));
          }
        }
      }
      // Check for alternative multi-item format (items)
      else if (map.containsKey('items') && map['items'] is List) {
        final itemsData = map['items'] as List<dynamic>;
        for (final itemData in itemsData) {
          if (itemData is Map<String, dynamic>) {
            items.add(OrderItem.fromMap(itemData));
          }
        }
      }
      // Handle legacy single item format (Admin app) - convert to multi-item
      else {
        final productId = map['productId'] ?? map['product_uid'] ?? map['product_id'] ?? '';
        final quantity = map['quantity'] ?? 1;
        final totalAmount = map['totalAmount'] ?? map['total_amount'] ?? map['total'] ?? 0;

        if (productId.isNotEmpty) {
          // Try to get product name from various possible fields
          final productName = map['productName'] ?? map['product_name'] ?? 'Product $productId';
          final productImage = map['productImage'] ?? map['product_image'];

          items.add(OrderItem(
            id: '${id}_item_1',
            productId: productId,
            productName: productName,
            productImage: productImage,
            productPrice: quantity > 0 ? (totalAmount / quantity).toDouble() : totalAmount.toDouble(),
            quantity: quantity,
            totalPrice: totalAmount.toDouble(),
          ));
        }
      }

      return OrderModel(
        id: id,
        // Handle both userId and user_id field names
        userId: map['userId'] ?? map['user_id'] ?? '',
        items: items,
        // Handle both orderDate and order_date field names
        orderDate: map['orderDate'] ?? map['order_date'] ?? '',
        status: _stringToOrderStatus(map['status'] ?? 'pending'),
        // Handle both shippingAddress and shipping_address field names (string and object formats)
        shippingAddress: _parseShippingAddress(map),
        // Handle both trackingNumber and tracking_number field names
        trackingNumber: map['trackingNumber'] ?? map['tracking_number'],
        shippedDate: map['shippedDate']?.toDate(),
        deliveredDate: map['deliveredDate']?.toDate(),
        cancelReason: map['cancelReason'] ?? map['cancel_reason'],
        returnReason: map['returnReason'] ?? map['return_reason'],
        totalAmount: map['totalAmount'] ?? map['total_amount'] ?? map['amount'] ?? 0,
        // Parse payment method from user app's paymentInfo object
        paymentMethod: _parsePaymentMethod(map),
        // Handle both isPaid and is_paid field names
        isPaid: map['isPaid'] ?? map['is_paid'] ?? false,
        createdAt: (map['createdAt'] as Timestamp?)?.toDate() ??
                   (map['created_at'] as Timestamp?)?.toDate() ??
                   DateTime.now(),
        updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ??
                   (map['updated_at'] as Timestamp?)?.toDate(),
        // Parse customer information from all possible sources (enhanced for user app compatibility)
        customerName: _parseCustomerName(map),
        customerEmail: _parseCustomerEmail(map),
        customerPhone: _parseCustomerPhone(map),
        estimatedDelivery: map['estimatedDelivery'] ?? map['estimated_delivery'],
        notes: map['notes'],
        // Parse payment information (Razorpay integration)
        paymentId: map['paymentId'] ?? map['payment_id'],
        razorpayOrderId: map['razorpayOrderId'] ?? map['razorpay_order_id'] ?? map['order_id'],
        paymentSignature: map['paymentSignature'] ?? map['payment_signature'] ?? map['signature'],
        isPaymentVerified: map['isPaymentVerified'] ?? map['is_payment_verified'] ?? map['verified'] ?? false,
      );
    } catch (e) {
      // Return minimal valid order on error
      return OrderModel(
        id: id,
        userId: '',
        items: [],
        orderDate: DateTime.now().toIso8601String(),
        status: OrderStatus.pending,
        totalAmount: 0,
        paymentMethod: '',
        isPaid: false,
        createdAt: DateTime.now(),
      );
    }
  }

  static OrderStatus _stringToOrderStatus(String status) {
    switch (status) {
      case 'pending':
        return OrderStatus.pending;
      case 'processing':
        return OrderStatus.processing;
      case 'shipped':
        return OrderStatus.shipped;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      case 'returned':
        return OrderStatus.returned;
      default:
        return OrderStatus.pending;
    }
  }

  // Helper method to parse shipping address from various formats
  static String? _parseShippingAddress(Map<String, dynamic> map) {
    // Try direct string fields first
    final directAddress = map['shippingAddress'] ?? map['shipping_address'];
    if (directAddress != null && directAddress is String && directAddress.isNotEmpty) {
      return directAddress;
    }

    // Try to parse from structured address object (User app format)
    final addressObj = map['shippingAddress'] ?? map['shipping_address'];
    if (addressObj != null && addressObj is Map<String, dynamic>) {
      final parts = <String>[];

      // Add receiver name
      final receiver = addressObj['receiver'] ?? addressObj['full_name'];
      if (receiver != null && receiver.toString().isNotEmpty) {
        parts.add('📍 ${receiver.toString()}');
      }

      // Add address lines
      final addressLine1 = addressObj['address_line_1'] ?? addressObj['addressLine1'];
      if (addressLine1 != null && addressLine1.toString().isNotEmpty) {
        parts.add(addressLine1.toString());
      }

      final addressLine2 = addressObj['address_line_2'] ?? addressObj['addressLine2'];
      if (addressLine2 != null && addressLine2.toString().isNotEmpty) {
        parts.add(addressLine2.toString());
      }

      // Add city, state, pincode
      final city = addressObj['city'];
      final state = addressObj['state'];
      final pincode = addressObj['pincode'];

      final locationParts = <String>[];
      if (city != null && city.toString().isNotEmpty) {
        locationParts.add(city.toString());
      }
      if (state != null && state.toString().isNotEmpty) {
        locationParts.add(state.toString());
      }
      if (pincode != null && pincode.toString().isNotEmpty) {
        locationParts.add(pincode.toString());
      }

      if (locationParts.isNotEmpty) {
        parts.add(locationParts.join(', '));
      }

      // Add country if available
      final country = addressObj['country'];
      if (country != null && country.toString().isNotEmpty && country.toString() != 'India') {
        parts.add(country.toString());
      }

      // Add phone if available
      final phone = addressObj['phone'];
      if (phone != null && phone.toString().isNotEmpty) {
        parts.add('📞 ${phone.toString()}');
      }

      return parts.isNotEmpty ? parts.join('\n') : null;
    }

    return null;
  }

  // Helper method to parse customer name from various sources
  static String? _parseCustomerName(Map<String, dynamic> map) {
    // Try direct fields first
    String? name = map['customerName'] ?? map['customer_name'];
    if (name != null && name.isNotEmpty) return name;

    // Try user app's customerInfo object (primary format)
    if (map['customerInfo'] is Map<String, dynamic>) {
      final customerInfo = map['customerInfo'] as Map<String, dynamic>;
      name = customerInfo['name'];
      if (name != null && name.isNotEmpty) return name;
    }

    // Try nested customer object
    if (map['customer'] is Map<String, dynamic>) {
      final customer = map['customer'] as Map<String, dynamic>;
      name = customer['name'];
      if (name != null && name.isNotEmpty) return name;
    }

    // Try customer_details object (legacy format)
    if (map['customer_details'] is Map<String, dynamic>) {
      final customerDetails = map['customer_details'] as Map<String, dynamic>;
      name = customerDetails['name'];
      if (name != null && name.isNotEmpty) return name;
    }

    // Try shipping address receiver as fallback
    if (map['shippingAddress'] is Map<String, dynamic>) {
      final shippingAddress = map['shippingAddress'] as Map<String, dynamic>;
      name = shippingAddress['receiver'] ?? shippingAddress['full_name'];
      if (name != null && name.isNotEmpty) return name;
    }

    return null;
  }

  // Helper method to parse customer email from various sources
  static String? _parseCustomerEmail(Map<String, dynamic> map) {
    // Try direct fields first
    String? email = map['customerEmail'] ?? map['customer_email'];
    if (email != null && email.isNotEmpty) return email;

    // Try user app's customerInfo object (primary format)
    if (map['customerInfo'] is Map<String, dynamic>) {
      final customerInfo = map['customerInfo'] as Map<String, dynamic>;
      email = customerInfo['email'];
      if (email != null && email.isNotEmpty) return email;
    }

    // Try nested customer object
    if (map['customer'] is Map<String, dynamic>) {
      final customer = map['customer'] as Map<String, dynamic>;
      email = customer['email'];
      if (email != null && email.isNotEmpty) return email;
    }

    // Try customer_details object (legacy format)
    if (map['customer_details'] is Map<String, dynamic>) {
      final customerDetails = map['customer_details'] as Map<String, dynamic>;
      email = customerDetails['email'];
      if (email != null && email.isNotEmpty) return email;
    }

    return null;
  }

  // Helper method to parse customer phone from various sources
  static String? _parseCustomerPhone(Map<String, dynamic> map) {
    // Try direct fields first
    String? phone = map['customerPhone'] ?? map['customer_phone'];
    if (phone != null && phone.isNotEmpty) return phone;

    // Try user app's customerInfo object (primary format)
    if (map['customerInfo'] is Map<String, dynamic>) {
      final customerInfo = map['customerInfo'] as Map<String, dynamic>;
      phone = customerInfo['phone'];
      if (phone != null && phone.isNotEmpty) return phone;
    }

    // Try nested customer object
    if (map['customer'] is Map<String, dynamic>) {
      final customer = map['customer'] as Map<String, dynamic>;
      phone = customer['phone'];
      if (phone != null && phone.isNotEmpty) return phone;
    }

    // Try customer_details object (legacy format)
    if (map['customer_details'] is Map<String, dynamic>) {
      final customerDetails = map['customer_details'] as Map<String, dynamic>;
      phone = customerDetails['phone'];
      if (phone != null && phone.isNotEmpty) return phone;
    }

    return null;
  }

  // Helper method to parse payment method from various sources
  static String _parsePaymentMethod(Map<String, dynamic> map) {
    // Try direct fields first
    String? paymentMethod = map['paymentMethod'] ?? map['payment_method'];
    if (paymentMethod != null && paymentMethod.isNotEmpty) return paymentMethod;

    // Try user app's paymentInfo object (primary format)
    if (map['paymentInfo'] is Map<String, dynamic>) {
      final paymentInfo = map['paymentInfo'] as Map<String, dynamic>;
      paymentMethod = paymentInfo['method'] ?? paymentInfo['paymentMethod'];
      if (paymentMethod != null && paymentMethod.isNotEmpty) return paymentMethod;
    }

    return 'Unknown';
  }

  // Debug method to test customer parsing
  static Map<String, String?> debugCustomerParsing(Map<String, dynamic> map) {
    return {
      'customerName': _parseCustomerName(map),
      'customerEmail': _parseCustomerEmail(map),
      'customerPhone': _parseCustomerPhone(map),
      'raw_customer_name': map['customer_name']?.toString(),
      'raw_customerName': map['customerName']?.toString(),
      'raw_customer_object': map['customer']?.toString(),
      'raw_customer_details': map['customer_details']?.toString(),
    };
  }

  // Create a copy of this order with some fields updated
  OrderModel copyWith({
    String? id,
    String? userId,
    List<OrderItem>? items,
    String? orderDate,
    OrderStatus? status,
    String? shippingAddress,
    String? trackingNumber,
    DateTime? shippedDate,
    DateTime? deliveredDate,
    String? cancelReason,
    String? returnReason,
    num? totalAmount,
    String? paymentMethod,
    bool? isPaid,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    String? estimatedDelivery,
    String? notes,
    String? paymentId,
    String? razorpayOrderId,
    String? paymentSignature,
    bool? isPaymentVerified,
  }) {
    return OrderModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      items: items ?? this.items,
      orderDate: orderDate ?? this.orderDate,
      status: status ?? this.status,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      shippedDate: shippedDate ?? this.shippedDate,
      deliveredDate: deliveredDate ?? this.deliveredDate,
      cancelReason: cancelReason ?? this.cancelReason,
      returnReason: returnReason ?? this.returnReason,
      totalAmount: totalAmount ?? this.totalAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      isPaid: isPaid ?? this.isPaid,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      customerPhone: customerPhone ?? this.customerPhone,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      notes: notes ?? this.notes,
      paymentId: paymentId ?? this.paymentId,
      razorpayOrderId: razorpayOrderId ?? this.razorpayOrderId,
      paymentSignature: paymentSignature ?? this.paymentSignature,
      isPaymentVerified: isPaymentVerified ?? this.isPaymentVerified,
    );
  }
}

// Order Item Model for multi-item support
class OrderItem {
  final String id;
  final String productId;
  final String productName;
  final String? productImage;
  final double productPrice;
  final int quantity;
  final double totalPrice;

  OrderItem({
    required this.id,
    required this.productId,
    required this.productName,
    this.productImage,
    required this.productPrice,
    required this.quantity,
    required this.totalPrice,
  });

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    // Handle various data formats from user app
    final productId = map['product_id'] ?? map['productId'] ?? map['product_uid'] ?? '';
    final productName = map['product_name'] ?? map['productName'] ?? map['title'] ?? 'Product $productId';
    final productImage = map['product_image'] ?? map['productImage'] ?? map['image'] ??
                        (map['images'] is List && (map['images'] as List).isNotEmpty ? (map['images'] as List).first : null);
    final productPrice = (map['product_price'] ?? map['productPrice'] ?? map['price'] ?? map['discountPrice'] ?? 0).toDouble();
    final quantity = map['quantity'] ?? 1;
    final totalPrice = (map['total_price'] ?? map['totalPrice'] ?? map['total'] ?? (productPrice * quantity)).toDouble();

    return OrderItem(
      id: map['id'] ?? map['item_id'] ?? map['product_id'] ?? '',
      productId: productId,
      productName: productName,
      productImage: productImage,
      productPrice: productPrice,
      quantity: quantity,
      totalPrice: totalPrice,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': id,
      'product_id': productId,
      'productId': productId,
      'product_name': productName,
      'productName': productName,
      'product_image': productImage,
      'productImage': productImage,
      'product_price': productPrice,
      'productPrice': productPrice,
      'quantity': quantity,
      'total_price': totalPrice,
      'totalPrice': totalPrice,
    };
  }
}
