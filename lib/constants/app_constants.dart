import 'package:flutter/material.dart';

import 'shared_constants.dart';

// App Constants
const String appName = "Aster Computers Admin";
const String appVersion = "1.0.0";

// Colors
const Color primaryColor = Color(0xFF3366FF);
const Color secondaryColor = Color(0xFF2A2D3E);
const Color bgColor = Color(0xFF212332);
const Color textColor = Color(0xFF8D8D8D);
const Color successColor = Color(0xFF00C853);
const Color warningColor = Color(0xFFFFD600);
const Color errorColor = Color(0xFFFF5252);

// Padding
const double defaultPadding = 16.0;

// Firebase Collections - Using shared constants

const String usersCollection = FirebaseCollections.users;
const String productsCollection = FirebaseCollections.products;
const String ordersCollection = FirebaseCollections.orders;
const String addressesCollection = FirebaseCollections.addresses;
const String cartCollection = FirebaseCollections.cart;
const String reviewsCollection = FirebaseCollections.reviews;
const String carouselCollection = FirebaseCollections.carouselItems;
const String adminCollection = FirebaseCollections.admins;
const String categoriesCollection = FirebaseCollections.categories;
const String brandsCollection = FirebaseCollections.brands;
const String couponsCollection = FirebaseCollections.coupons;
const String activityLogsCollection = FirebaseCollections.activityLogs;
const String settingsCollection = FirebaseCollections.settings;

// Admin Roles
enum AdminRole {
  superAdmin,
  admin,
  editor,
  viewer,
}

// Order Status
enum OrderStatus {
  pending,
  processing,
  shipped,
  delivered,
  cancelled,
  returned,
}

// Product Status
enum ProductStatus {
  active,
  inactive,
  outOfStock,
  discontinued,
}
