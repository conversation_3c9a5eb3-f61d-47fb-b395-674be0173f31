// Shared constants between Admin and User apps
// This file should be kept in sync between both applications

// Firebase Collections - Standardized names
class FirebaseCollections {
  static const String users = "users";
  static const String products = "products";
  static const String orders = "ordered_products";
  static const String addresses = "addresses";
  static const String cart = "cart";
  static const String reviews = "reviews";
  static const String carouselItems = "carousel_items";
  static const String admins = "admins";
  static const String categories = "categories";
  static const String brands = "brands";
  static const String coupons = "coupons";
  static const String activityLogs = "activity_logs";
  static const String settings = "settings";
}

// Order Field Names - Support both formats
class OrderFields {
  // Primary field names (Admin app format)
  static const String id = "id";
  static const String userId = "userId";
  static const String productId = "productId";
  static const String orderDate = "orderDate";
  static const String status = "status";
  static const String shippingAddress = "shippingAddress";
  static const String trackingNumber = "trackingNumber";
  static const String shippedDate = "shippedDate";
  static const String deliveredDate = "deliveredDate";
  static const String cancelReason = "cancelReason";
  static const String returnReason = "returnReason";
  static const String totalAmount = "totalAmount";
  static const String quantity = "quantity";
  static const String paymentMethod = "paymentMethod";
  static const String isPaid = "isPaid";
  static const String createdAt = "createdAt";
  static const String updatedAt = "updatedAt";
  
  // Alternative field names (User app format)
  static const String userIdAlt = "user_id";
  static const String productIdAlt = "product_uid";
  static const String orderDateAlt = "order_date";
  static const String shippingAddressAlt = "shipping_address";
  static const String trackingNumberAlt = "tracking_number";
  static const String shippedDateAlt = "shipped_date";
  static const String deliveredDateAlt = "delivered_date";
  static const String cancelReasonAlt = "cancel_reason";
  static const String returnReasonAlt = "return_reason";
  static const String totalAmountAlt = "total_amount";
  static const String paymentMethodAlt = "payment_method";
  static const String isPaidAlt = "is_paid";
  static const String createdAtAlt = "created_at";
  static const String updatedAtAlt = "updated_at";
  static const String customerName = "customerName";
  static const String estimatedDelivery = "estimatedDelivery";
  static const String notes = "notes";
}

// Order Status - Unified constants
class OrderStatus {
  static const String pending = "pending";
  static const String processing = "processing";
  static const String shipped = "shipped";
  static const String delivered = "delivered";
  static const String cancelled = "cancelled";
  static const String returned = "returned";
  
  // All valid statuses
  static const List<String> allStatuses = [
    pending,
    processing,
    shipped,
    delivered,
    cancelled,
    returned,
  ];
  
  // Status validation
  static bool isValidStatus(String status) {
    return allStatuses.contains(status.toLowerCase());
  }
  
  // Status display names
  static String getDisplayName(String status) {
    switch (status.toLowerCase()) {
      case pending:
        return 'Pending';
      case processing:
        return 'Processing';
      case shipped:
        return 'Shipped';
      case delivered:
        return 'Delivered';
      case cancelled:
        return 'Cancelled';
      case returned:
        return 'Returned';
      default:
        return status;
    }
  }
}

// Payment Methods - Unified constants
class PaymentMethods {
  static const String cashOnDelivery = "Cash on Delivery";
  static const String onlinePayment = "Online Payment";
  static const String creditCard = "Credit/Debit Card";
  static const String upi = "UPI";
  static const String razorpay = "Razorpay";
  
  static const List<String> allMethods = [
    cashOnDelivery,
    onlinePayment,
    creditCard,
    upi,
    razorpay,
  ];
}

// Collection Paths - Standardized paths
class CollectionPaths {
  // User orders in subcollection (Admin app format)
  static String userOrders(String userId) => 
      "${FirebaseCollections.users}/$userId/${FirebaseCollections.orders}";
  
  // Root orders collection (User app format)
  static String rootOrders() => FirebaseCollections.orders;
  
  // User document
  static String user(String userId) => 
      "${FirebaseCollections.users}/$userId";
  
  // Product document
  static String product(String productId) => 
      "${FirebaseCollections.products}/$productId";
}

// Data Validation Helpers
class DataValidation {
  static bool isValidUserId(String? userId) {
    return userId != null && userId.isNotEmpty;
  }
  
  static bool isValidOrderId(String? orderId) {
    return orderId != null && orderId.isNotEmpty;
  }
  
  static bool isValidProductId(String? productId) {
    return productId != null && productId.isNotEmpty;
  }
  
  static bool isValidAmount(num? amount) {
    return amount != null && amount >= 0;
  }
  
  static bool isValidQuantity(int? quantity) {
    return quantity != null && quantity > 0;
  }
}

// Error Messages
class ErrorMessages {
  static const String invalidUserId = "User ID cannot be empty";
  static const String invalidOrderId = "Order ID cannot be empty";
  static const String invalidProductId = "Product ID cannot be empty";
  static const String invalidAmount = "Amount must be greater than or equal to 0";
  static const String invalidQuantity = "Quantity must be greater than 0";
  static const String invalidStatus = "Invalid order status";
  static const String orderNotFound = "Order not found";
  static const String userNotFound = "User not found";
  static const String productNotFound = "Product not found";
  static const String updateFailed = "Failed to update order";
  static const String createFailed = "Failed to create order";
  static const String deleteFailed = "Failed to delete order";
}
