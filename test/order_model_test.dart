import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../lib/models/order_model.dart';
import '../lib/constants/app_constants.dart';

void main() {
  group('OrderModel Tests', () {
    test('should create OrderModel from single-item format (Admin app)', () {
      final map = {
        'userId': 'user123',
        'productId': 'product456',
        'orderDate': '2024-01-01',
        'status': 'pending',
        'totalAmount': 100.0,
        'quantity': 2,
        'paymentMethod': 'Credit Card',
        'isPaid': true,
        'createdAt': Timestamp.now(),
      };

      final order = OrderModel.fromMap(map, 'order123');

      expect(order.id, 'order123');
      expect(order.userId, 'user123');
      expect(order.items.length, 1);
      expect(order.items.first.productId, 'product456');
      expect(order.items.first.quantity, 2);
      expect(order.productId, 'product456'); // Legacy getter
      expect(order.quantity, 2); // Legacy getter
      expect(order.totalAmount, 100.0);
      expect(order.status, OrderStatus.pending);
    });

    test('should create OrderModel from multi-item format (User app)', () {
      final map = {
        'user_id': 'user123',
        'order_date': '2024-01-01',
        'status': 'pending',
        'totalAmount': 150.0,
        'paymentMethod': 'UPI',
        'isPaid': false,
        'createdAt': Timestamp.now(),
        'order_items': [
          {
            'id': 'item1',
            'product_id': 'product456',
            'product_name': 'Laptop',
            'product_price': 100.0,
            'quantity': 1,
            'total_price': 100.0,
          },
          {
            'id': 'item2',
            'product_id': 'product789',
            'product_name': 'Mouse',
            'product_price': 50.0,
            'quantity': 1,
            'total_price': 50.0,
          },
        ],
      };

      final order = OrderModel.fromMap(map, 'order123');

      expect(order.id, 'order123');
      expect(order.userId, 'user123');
      expect(order.items.length, 2);
      expect(order.items.first.productId, 'product456');
      expect(order.items.first.productName, 'Laptop');
      expect(order.items.last.productId, 'product789');
      expect(order.items.last.productName, 'Mouse');
      expect(order.quantity, 2); // Total quantity
      expect(order.isMultiItem, true);
      expect(order.totalAmount, 150.0);
    });

    test('should handle legacy single-item constructor', () {
      final order = OrderModel.fromSingleItem(
        id: 'order123',
        userId: 'user123',
        productId: 'product456',
        orderDate: '2024-01-01',
        status: OrderStatus.pending,
        totalAmount: 100.0,
        quantity: 2,
        paymentMethod: 'Credit Card',
        isPaid: true,
        createdAt: DateTime.now(),
      );

      expect(order.items.length, 1);
      expect(order.items.first.productId, 'product456');
      expect(order.items.first.quantity, 2);
      expect(order.productId, 'product456');
      expect(order.quantity, 2);
      expect(order.isMultiItem, false);
    });

    test('should convert to map with both field formats', () {
      final order = OrderModel.fromSingleItem(
        id: 'order123',
        userId: 'user123',
        productId: 'product456',
        orderDate: '2024-01-01',
        status: OrderStatus.pending,
        totalAmount: 100.0,
        quantity: 2,
        paymentMethod: 'Credit Card',
        isPaid: true,
        createdAt: DateTime.now(),
      );

      final map = order.toMap();

      // Check both field formats exist
      expect(map['userId'], 'user123');
      expect(map['user_id'], 'user123');
      expect(map['productId'], 'product456');
      expect(map['product_uid'], 'product456');
      expect(map['orderDate'], '2024-01-01');
      expect(map['order_date'], '2024-01-01');
      expect(map['paymentMethod'], 'Credit Card');
      expect(map['payment_method'], 'Credit Card');
      expect(map['isPaid'], true);
      expect(map['is_paid'], true);

      // Check multi-item format
      expect(map['order_items'], isA<List>());
      expect(map['items'], isA<List>());
      expect((map['order_items'] as List).length, 1);
    });

    test('should handle copyWith correctly', () {
      final originalOrder = OrderModel.fromSingleItem(
        id: 'order123',
        userId: 'user123',
        productId: 'product456',
        orderDate: '2024-01-01',
        status: OrderStatus.pending,
        totalAmount: 100.0,
        quantity: 2,
        paymentMethod: 'Credit Card',
        isPaid: false,
        createdAt: DateTime.now(),
      );

      final updatedOrder = originalOrder.copyWith(
        status: OrderStatus.shipped,
        isPaid: true,
      );

      expect(updatedOrder.id, originalOrder.id);
      expect(updatedOrder.userId, originalOrder.userId);
      expect(updatedOrder.status, OrderStatus.shipped);
      expect(updatedOrder.isPaid, true);
      expect(updatedOrder.items.length, 1);
    });

    test('should handle error gracefully', () {
      final invalidMap = {
        'invalid': 'data',
      };

      final order = OrderModel.fromMap(invalidMap, 'order123');

      expect(order.id, 'order123');
      expect(order.userId, '');
      expect(order.items.isEmpty, true);
      expect(order.status, OrderStatus.pending);
      expect(order.totalAmount, 0);
    });
  });
}
