name: aster_computers_admin
description: "Admin application for Aster Computers"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

  # Firebase
  firebase_core: ^2.27.1
  firebase_auth: ^4.17.9
  cloud_firestore: ^4.15.9
  firebase_storage: ^11.6.10
  firebase_app_check: ^0.2.1+14
  firebase_messaging: ^14.7.20
  cloud_functions: ^4.6.10
  flutter_local_notifications: ^16.3.2

  # State Management
  provider: ^6.1.2

  # UI Components
  flutter_admin_scaffold: ^1.2.0
  data_table_2: ^2.5.10
  fl_chart: ^0.66.2
  syncfusion_flutter_charts: ^24.2.8
  syncfusion_flutter_datagrid: ^24.2.8
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  image_picker: ^1.0.7
  carousel_slider: ^4.2.1
  file_picker: ^5.5.0
  flutter_easyloading: ^3.0.5
  intl: ^0.19.0

  # Utils
  shared_preferences: ^2.2.2
  url_launcher: ^6.3.1
  logger: ^2.0.2+1
  http: ^1.4.0
  path_provider: ^2.1.2
  pdf: ^3.10.7
  printing: ^5.11.1
  flutter_carousel_widget: ^3.1.0
  google_maps_flutter: ^2.12.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/

  fonts:
    - family: Muli
      fonts:
        - asset: assets/fonts/muli/Muli.ttf
        - asset: assets/fonts/muli/Muli-Bold.ttf
          weight: 700
        - asset: assets/fonts/muli/Muli-Light.ttf
          weight: 300
